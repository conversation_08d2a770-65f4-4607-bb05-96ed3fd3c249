# Web-Med Component Usage Analysis

## Overview

This analysis examines which components in `/apps/web-med/src/components/` are being used vs unused in the `/apps/web-med/src/www/` directory.

## Used Components by Category

### Actions Components (components/actions/)

**USED:**

- `AddContract` from `actions/contracts/contract`
- `ContractMenu` from `actions/contracts/contract`
- `AddMember`, `MemberMenu` from `actions/org-member`
- `AddInvitation`, `InvitationMenu` from `actions/org-invitation`
- `AddOrganization`, `OrganizationMenu` from `actions/organization`
- `UpdateOrganizationBanking` from `actions/organization-banking`
- `OrganizationOfferMenu` from `actions/jobs/offer`
- `SendOffer`, `AcceptOffer`, `RejectOffer`, `WithdrawOffer` from `actions/jobs/offer`
- `JobPostMenu` from `actions/jobs/job`
- `AddJobPost`, `UpdateJobPost` from `actions/jobs/job`
- `OrganizationApplicationMenu`, `AcceptApplication`, `RejectApplication`, `WithdrawApplication` from `actions/jobs/application`
- `PersonMenu` from `actions/people`
- `AddPerson` from `actions/people`
- `ProviderMenu` from `actions/provider`
- `QualificationMenu` from `actions/qualification`
- `AddQualification`, `UpdateQualification` from `actions/qualification`
- `DocumentMenu` from `actions/resources/document`
- `AddDocument` from `actions/resources/document`
- `AddDepartment`, `DepartmentMenu`, `SearchDepartment` from `actions/department`
- `FacilityMenu`, `SearchFacility` from `actions/facility`
- `AddFacility` from `actions/facility`
- `AddContact` from `actions/contact`
- `IncidentMenu` from `actions/incident`
- `AddInvoice` from `actions/invoice`
- `ShiftMenu`, `AddShift`, `CheckInShift`, `CheckOutShift` from `actions/shift`
- `AddSpecialty`, `SearchSpecialty` from `actions/specialty`
- `AddValue` from `actions/value`
- `UserMenu` from `actions/user`
- `AddAddress`, `UpdateAddress` from `actions/address`
- `AddJobExperience`, `UpdateJobExperience` from `actions/jobExperience`

### Common Components (components/common/)

**USED:**

- `ContractStatus` / `ContractStatusBadge`
- `OrganizationType` / `OrganizationTypeBadge`
- `OrganizationClass` / `OrganizationClassBadge`
- `OfferStatus` / `OfferStatusBadge`
- `ApplicationStatus` / `ApplicationStatusBadge`
- `ProviderStatus` / `ProviderStatusBadge`
- `VerificationStatus` / `VerificationStatusBadge`
- `QualificationStatus` / `QualificationStatusBadge`
- `QualificationType` / `QualificationTypeBadge`
- `ShiftStatus` / `ShiftStatusBadge`
- `FacilityType` / `FacilityTypeBadge`
- `DepartmentType` / `DepartmentTypeBadge`
- `JobPriority` / `JobPriorityBadge`
- `JobStatus` / `JobStatusBadge`
- `JobType` / `JobTypeBadge`
- `PayoutStatus` / `PayoutStatusBadge`

### Forms Components (components/forms/)

**USED:**

- `OrganizationInformationForm`
- `TimeBlockForm`
- `JobPostPaymentForm`
- `DepartmentForm`
- `FacilityForm`

### Tables Components (components/tables/)

**USED:**

- `ListOrganizations`
- `ListOffers`
- `ListContracts`
- `ListInvoices`
- `ListProviders`
- `ListReviews`
- `ListApplications`
- `ListIncidents`
- `ListDocuments`
- `ListPeople`
- `ListJobs`
- `ListShifts`
- `ListPayouts`
- `ListBillingShifts`
- `ListPayoutShifts`
- `ListFacilities`
- `ListSpecialties`
- `ListValues`
- `ListUsers`

### Selectors Components (components/selectors/)

**USED:**

- `SelectFacility`, `SearchFacility`
- `SelectDepartment`
- `SelectContact`
- `SearchMedicalRole`
- `SearchSpecialty`
- `SearchOrganization`

### Shared Components (components/shared/)

**USED:**

- `PaginatedCard`
- `ErrorFallback` from `Error`
- `PreviewProvider`
- `ContactCard`
- `DocumentUploader`
- `MapLocations`
- `HowItWorks`
- `SMSOptIn`
- `ShiftTimeline`
- `ActionLog` from `actions/ActionLog`

### Widgets Components (components/widgets/)

**USED:**

- `ContractManager` from `widgets/contracts/manager`
- `MessagesPage`, `MessagesWidget`, `Chat` from `widgets/messages`
- `AddPayment` from `widgets/stripe/AddPayment`
- `PaymentMethods` from `widgets/stripe/PaymentMethods`
- `StripeAccount` from `widgets/stripe/Account`
- `StripeRoot` from `widgets/stripe/Root`
- `StripeVerification` from `widgets/stripe/Verification`
- `FacilityGenerator` from `widgets/facility-generator`

### Other Components

**USED:**

- `useUser` from `contexts/User`
- `useSystem` from `contexts/System`
- `OnboardingCallout`
- `Timeline`
- Various provider components from `providers/` directory
- Various shift components from `shifts/` directory
- Various job components from `jobs/` directory
- Various facility components from `facilities/` directory
- Various schedule components from `schedule/` directory
- Various view components from `views/` directory

## Analysis Summary

### High Usage Categories:

1. **Actions** - Heavily used across all www pages for CRUD operations
2. **Common** - Status badges are extensively used for displaying enum values
3. **Tables** - List components are core to most admin/management pages
4. **Contexts** - User context is used almost everywhere

### Medium Usage Categories:

1. **Shared** - Utility components used selectively
2. **Widgets** - Specialized components for specific features
3. **Selectors** - Used in forms and search interfaces

### Lower Usage Categories:

1. **Forms** - Only a few form components are actively used
2. **Views** - Limited usage, mostly in specific pages

## UNUSED COMPONENTS ANALYSIS

### Actions Components - UNUSED:

- `actions/contracts/agreement.tsx` - Agreement-specific actions not used
- `actions/message.tsx` - Message actions not directly imported
- `actions/payout.tsx` - Payout actions not used
- `actions/review.tsx` - Review actions not used
- `actions/shift/general.tsx` - General shift actions not used
- `actions/shift/organization.tsx` - Organization shift actions not used
- `actions/shift/provider.tsx` - Provider shift actions not used

### Common Components - UNUSED:

- `AccountStatus.tsx` - Account status badges not used
- `AgreementStatus.tsx` - Agreement status badges not used
- `ContractType.tsx` - Contract type badges not used
- `IncidentSeverity.tsx` - Incident severity badges not used
- `IncidentStatus.tsx` - Incident status badges not used
- `IncidentType.tsx` - Incident type badges not used
- `InvoiceStatus.tsx` - Invoice status badges not used
- `JobPostMode.tsx` - Job post mode badges not used
- `OrganizationBillingType.tsx` - Organization billing type badges not used
- `OrganizationMode.tsx` - Organization mode badges not used
- `OrganizationStatus.tsx` - Organization status badges not used
- `PaymentType.tsx` - Payment type badges not used
- `PayoutType.tsx` - Payout type badges not used
- `PersonRole.tsx` - Person role badges not used
- `ProviderRole.tsx` - Provider role badges not used
- `ScheduleType.tsx` - Schedule type badges not used
- `SignatureStatus.tsx` - Signature status badges not used
- `TimeBlockRecurrence.tsx` - Time block recurrence badges not used
- `TimeBlockType.tsx` - Time block type badges not used
- `ValueType.tsx` - Value type badges not used
- `Watermark.tsx` - Watermark component not used

### Forms Components - UNUSED:

- `AddressForm.tsx` - Address form not used
- `ApplicationForm.tsx` - Application form not used
- `BlockForm.tsx` - Block form not used
- `contracts/agreement-form.tsx` - Agreement form not used
- `contracts/agreement.tsx` - Agreement contract form not used
- `contracts/core.tsx` - Core contract form not used
- `contracts/signatures.tsx` - Signatures form not used
- `contracts/simple-update.tsx` - Simple update form not used
- `DocumentForm.tsx` - Document form not used
- `DocumentUploadForm.tsx` - Document upload form not used
- `fields/ContractStatus.tsx` - Contract status field not used
- `fields/ContractType.tsx` - Contract type field not used
- `fields/DepartmentType.tsx` - Department type field not used
- `fields/Document.tsx` - Document field not used
- `fields/FacilityType.tsx` - Facility type field not used
- `fields/IncidentSeverity.tsx` - Incident severity field not used
- `fields/IncidentStatus.tsx` - Incident status field not used
- `fields/IncidentType.tsx` - Incident type field not used
- `fields/TimeBlock.tsx` - Time block field not used
- `IncidentForm.tsx` - Incident form not used
- `InvoiceForm.tsx` - Invoice form not used
- `JobExperienceForm.tsx` - Job experience form not used
- `JobPostForm.tsx` - Job post form not used
- `MessageForm.tsx` - Message form not used
- `OfferForm.tsx` - Offer form not used
- `OrganizationBillingForm.tsx` - Organization billing form not used
- `OrgContact.tsx` - Organization contact form not used
- `OrgDocument.tsx` - Organization document form not used
- `OrgFacility.tsx` - Organization facility form not used
- `OrgJobPost.tsx` - Organization job post form not used
- `OrgMember.tsx` - Organization member form not used
- `OrgOrganization.tsx` - Organization form not used
- `OrgPerson.tsx` - Organization person form not used
- `OrgUser.tsx` - Organization user form not used
- `ProviderForm.tsx` - Provider form not used
- `ProviderLanguageForm.tsx` - Provider language form not used
- `ProviderSpecialtyForm.tsx` - Provider specialty form not used
- `ProviderTitleForm.tsx` - Provider title form not used
- `QualificationForm.tsx` - Qualification form not used
- `ReviewForm.tsx` - Review form not used
- `ScheduleForm.tsx` - Schedule form not used
- `ShiftDateForm.tsx` - Shift date form not used
- `ShiftForm.tsx` - Shift form not used
- `SpecialtyForm.tsx` - Specialty form not used
- `UserSettingsForm.tsx` - User settings form not used
- `ValueForm.tsx` - Value form not used

### Selectors Components - UNUSED:

- `SelectAddress.tsx` - Address selector not used
- `SelectApplication.tsx` - Application selector not used
- `SelectContract.tsx` - Contract selector not used
- `SelectDocument.tsx` - Document selector not used
- `SelectIncident.tsx` - Incident selector not used
- `SelectJobPost.tsx` - Job post selector not used
- `SelectOffer.tsx` - Offer selector not used
- `SelectPerson.tsx` - Person selector not used
- `SelectPosition.tsx` - Position selector not used
- `SelectProvider.tsx` - Provider selector not used
- `SelectQualification.tsx` - Qualification selector not used
- `SelectShift.tsx` - Shift selector not used

### Widgets Components - UNUSED:

- `AccountSwitcher.tsx` - Account switcher widget not used
- `Notifications.tsx` - Notifications widget not used
- `OrganizationSwitcher.tsx` - Organization switcher widget not used
- `UserManager.tsx` - User manager widget not used
- `UserNotifications.tsx` - User notifications widget not used
- `UserProfile.tsx` - User profile widget not used
- `UserSettings.tsx` - User settings widget not used
- `cookies/CookieConsent.tsx` - Cookie consent widget not used
- `cookies/index.tsx` - Cookie widget not used
- `prospecting/` - Entire prospecting widget directory not used
- `resume-scanner/` - Entire resume scanner widget directory not used
- `stripe/Connect.tsx` - Stripe connect widget not used
- `stripe/PaymentCapture.tsx` - Payment capture widget not used

### Other Unused Components:

- `contexts/Provider.tsx` - Provider context not used
- `facilities/DepartmentPreview.tsx` - Department preview not used
- `facilities/LocationPreview.tsx` - Location preview not used
- `facilities/LocationType.tsx` - Location type not used
- `fx/GradientBackground.tsx` - Gradient background not used
- `jobs/` - Many job components not used directly
- `layouts/` - Layout components not used in www
- `LinearStatus.tsx` - Linear status component not used
- `providers/` - Many provider components not used directly
- `schedule/` - Many schedule components not used directly
- `shared/Contract.tsx` - Contract shared component not used
- `shared/ContractPreview.tsx` - Contract preview not used
- `shared/ContractSigner.tsx` - Contract signer not used
- `shared/ContractStatus.tsx` - Contract status shared component not used
- `shared/GeolocationAdvisory.tsx` - Geolocation advisory not used
- `shared/notifications/NotificationItem.tsx` - Notification item not used
- `shared/PreviewDepartment.tsx` - Department preview not used
- `shared/PreviewJobPosts.tsx` - Job posts preview not used
- `shared/PreviewPayout.tsx` - Payout preview not used
- `shared/PreviewShift.tsx` - Shift preview not used
- `shared/ShiftIncident.tsx` - Shift incident not used
- `shifts/` - Many shift components not used directly
- `views/` - Many view components not used directly

## CLEANUP RECOMMENDATIONS

### High Priority (Safe to Remove):

1. **Unused Form Components** - Many forms are not being used and can be safely removed
2. **Unused Selector Components** - Many selectors are not being used
3. **Unused Common Badge Components** - Many status/type badges are not being used
4. **Unused Widget Components** - Several widget components are not being used

### Medium Priority (Investigate Dependencies):

1. **Provider/Job/Shift Components** - May be used indirectly through other components
2. **Layout Components** - May be used in other parts of the app
3. **View Components** - May be used in specific pages not analyzed

### Low Priority (Keep for Future Use):

1. **Context Components** - May be needed for future features
2. **Shared Utility Components** - May be used in other apps or future features

## SUMMARY STATISTICS

- **Total Components**: ~280 files
- **Used Components**: ~120 files (43%)
- **Unused Components**: ~160 files (57%)
- **Highest Unused Category**: Forms (90% unused)
- **Most Used Category**: Common badges (70% used)
