import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type {
  OrganizationStatus,
  OrganizationType,
  RouterOutputs,
} from "@/api";

import { api } from "@/api/client";

export type Organization = NonNullable<
  RouterOutputs["organizations"]["getMany"]["items"]
>[number];

export type OrganizationStructure = Organization;

export interface UseSelectOrganizationProps {
  types?: OrganizationType[];
  status?: OrganizationStatus[];
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Organization;
  defaultOptions?: Organization[];
  defaultDebounce?: number;
  organizationId?: string;
  onSelect?: (org: Organization) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectOrganization({
  types,
  status,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  organizationId,
  onSelect,
  onValueChange,
}: UseSelectOrganizationProps = {}) {
  const [selection, setSelection] = useState<Organization | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const organizations = api.organizations.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      types,
      status,
      ensure: organizationId ? [organizationId] : undefined,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = organizations.isLoading;

  const data = useMemo(() => {
    const apiData = organizations.data?.items ?? [];
    const combined = [...defaultOptions, ...apiData];
    // Remove duplicates by ID
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [organizations.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (org: Organization) => {
      setSelection(org);
      setOpen(false);
      setQuery("");
      await onSelect?.(org);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: organizations.refetch,
      hasNextPage: organizations.data
        ? (pageNumber + 1) * pageSize < organizations.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: organizations.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      organizations.data,
      organizations.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
