"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { PlusCircleIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { MedicalRoleValue } from "@/hooks/selectors/use-select-medical-role";

import { ValueType } from "@/api";
import { useSelectMedicalRole } from "@/hooks/selectors/use-select-medical-role";

import { AddValue } from "../actions/value";

const i18n = {
  en: {
    label: "Medical Role",
    description: "Select the medical role you want",
    placeholder: "Select a medical role",
    canNotFind: "Can't find what you're looking for?",
    form: {
      title: "Add Medical Role",
      description: "Add a new medical role",
      label: "Add Medical Role",
    },
  },
};

export type MedicalRoleStructure = MedicalRoleValue;
type PartialMedicalRoleSelector = Omit<SelectorProps<MedicalRoleValue>, "data">;

export interface SelectMedicalRoleProps extends PartialMedicalRoleSelector {
  open?: boolean;
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  defaultQuery?: string;
  defaultSelection?: MedicalRoleValue;
  defaultDebounce?: number;
  canNotFindText?: string;
  showAddValue?: boolean;
  size?: PartialMedicalRoleSelector["size"];
}

interface SelectMedicalRoleCoreProps extends PartialMedicalRoleSelector {
  data?: MedicalRoleValue[];
  loading?: boolean;
  selection?: MedicalRoleValue;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (role: MedicalRoleValue) => void | Promise<void>;
  onClear?: () => void;
  canNotFindText?: string;
  showAddValue?: boolean;
  refetch?: () => void;
}

function SelectMedicalRoleCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  canNotFindText = i18n.en.canNotFind,
  showAddValue = false,
  refetch,
  size = "md",
  className,
  ...props
}: SelectMedicalRoleCoreProps) {
  const enhancedData = useMemo(() => {
    const roles = [...data];
    if (selection && !roles.find((role) => role.id === selection.id)) {
      roles.unshift(selection);
    }
    return roles;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: MedicalRoleValue) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("text-base font-bold", {
            "text-sm": size === "md",
            "text-xs": size === "sm",
          })}
        >
          {item.value}
        </h3>
      </div>
    ),
    [size],
  );

  return (
    <Selector<MedicalRoleValue>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-18 p-2": size === "lg",
          "h-16 p-2": size === "md",
          "h-14 p-2": size === "sm",
        },
        className,
      )}
      label={i18n.en.placeholder}
      placeholder={i18n.en.placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      footer={
        showAddValue && (
          <div className="flex w-full flex-col items-center justify-center gap-2 pb-2 pt-4 text-center text-sm text-muted-foreground">
            {canNotFindText}{" "}
            <AddValue
              title={i18n.en.form.title}
              description={i18n.en.form.description}
              label={i18n.en.form.label}
              scope={ValueType.MEDICAL_ROLE}
              defaultValues={{
                type: ValueType.MEDICAL_ROLE,
                value: "",
              }}
              onSuccess={async (value) => {
                await refetch?.();
                const newRole: MedicalRoleValue = {
                  ...value,
                  type: "MEDICAL_ROLE",
                } as MedicalRoleValue;
                onValueChange?.("");
                await onSelect?.(newRole);
              }}
            >
              <Button variant="outline" className="w-full gap-2">
                <PlusCircleIcon className="size-4" />
                Add New Role
              </Button>
            </AddValue>
          </div>
        )
      }
    >
      {children}
    </Selector>
  );
}

export function SelectMedicalRole({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  showAddValue = true,
  organizationId,
  ...props
}: SelectMedicalRoleProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
    refetch,
  } = useSelectMedicalRole({
    enabled,
    organizationId,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectMedicalRoleCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
      showAddValue={showAddValue}
      refetch={refetch}
    />
  );
}

export interface SelectMedicalRoleFieldProps extends SelectMedicalRoleProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectMedicalRoleField({
  name = "role",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectMedicalRoleFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectMedicalRole
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchMedicalRoleProps extends SelectMedicalRoleProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchMedicalRole({
  group,
  name = "role",
  defaultValue,
  enabled = true,
  loading = false,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  showAddValue = false,
  organizationId,
  onSelect,
  useDialog = false,
  ...props
}: SearchMedicalRoleProps) {
  const {
    loading: hookLoading,
    data,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
    refetch,
  } = useSelectMedicalRole({
    enabled,
    organizationId,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } =
    useSearchValue<MedicalRoleValue>({
      name,
      group,
      defaultValue,
      data: data ?? [],
    });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (role: MedicalRoleValue) => {
      await setSelection(role);
      await onSelect?.(role);
      onSelectionChange(role);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectMedicalRoleCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      showAddValue={showAddValue}
      refetch={refetch}
      useDialog={useDialog}
    />
  );
}
