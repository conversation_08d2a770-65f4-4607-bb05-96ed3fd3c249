"use client";

import { useMemo } from "react";

import type {
  SearchOrganizationProps as BaseSearchOrganizationProps,
  SelectOrganizationFieldProps as SelectFieldProps,
  SelectOrganizationProps as SelectorProps,
} from "@axa/ui/selectors/SelectOrganization";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchOrganization as SearchOrganizationBase,
  SelectOrganizationField as SelectField,
  SelectOrganization as SelectOrganizationCore,
} from "@axa/ui/selectors/SelectOrganization";

import type { OrganizationStatus, OrganizationType } from "@/api";
import type { Organization } from "@/hooks/selectors/use-select-organization";

import { useSelectOrganization } from "@/hooks/selectors/use-select-organization";

const i18n = {
  en: {
    label: "Organization",
    description: "Select an organization",
    placeholder: "Select an organization",
    actions: {
      SelectOrganization: "Select Organization",
    },
  },
};

/**
 * Base component props for organization selector
 */
export interface SelectOrganizationProps
  extends Omit<SelectorProps<Organization>, "data"> {
  /** Organization types to filter by */
  types?: OrganizationType[];
  /** Organization status to filter by */
  status?: OrganizationStatus[];
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected organization */
  defaultSelection?: Organization;
  /** Default options to pre-populate */
  defaultOptions?: Organization[];
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Organization ID to ensure is included in results */
  organizationId?: string;
  /** Search group for integration */
  group?: string;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Organization selector component
 */
export function SelectOrganization({
  loading = false,
  enabled = true,
  useDialog = false,
  children,
  onSelect,
  types,
  status,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  organizationId,
  group,
  size = "lg",
  className,
  ...props
}: SelectOrganizationProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectOrganization({
    enabled,
    types,
    status,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    organizationId,
    group,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const orgs = [...data];
    if (selection && !orgs.find((org) => org.id === selection.id)) {
      orgs.unshift(selection);
    }
    return orgs;
  }, [data, selection]);

  return (
    <SelectOrganizationCore<Organization>
      useDialog={useDialog}
      size={size}
      className={className}
      {...props}
      data={enhancedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
    >
      {children}
    </SelectOrganizationCore>
  );
}

/**
 * Form field props for organization selector
 */
export interface SelectOrganizationFieldProps<
  T extends Organization = Organization,
> extends Omit<SelectFieldProps<T>, "data" | "onValueChange"> {
  /** Override loading state */
  loading?: boolean;
  /** Organization types to filter by */
  types?: OrganizationType[];
  /** Organization status to filter by */
  status?: OrganizationStatus[];
  /** Filter by organization ID */
  organizationId?: string;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Organization[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Field label (default: "Organization") */
  label?: string;
  /** Field description (default: "Select an organization") */
  description?: string;
  /** Placeholder text (default: "Select an organization") */
  placeholder?: string;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for organization selector
 */
export function SelectOrganizationField<T extends Organization = Organization>({
  types,
  status,
  organizationId,
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  size = "lg",
  className,
  ...props
}: SelectOrganizationFieldProps<T>) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectOrganization({
    enabled,
    types,
    status,
    organizationId,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectField
      label={label}
      description={description}
      placeholder={placeholder}
      size={size}
      className={className}
      {...props}
      loading={isLoading}
      data={data as unknown as T[]}
      onValueChange={setQuery}
    />
  );
}

/**
 * Search component props for organization selector
 */
export interface SearchOrganizationProps extends SelectOrganizationProps {
  /** Search group identifier */
  group: string;
  /** Field name (default: "organization") */
  name?: string;
  /** Default value */
  defaultValue?: string;
}

/**
 * Search component for organization selector
 */
export function SearchOrganization({
  group,
  name = "organization",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchOrganizationProps) {
  const { selection, onClear, onSelectionChange } =
    useSearchValue<Organization>({
      name,
      group,
      defaultValue,
      data: [], // Will be provided by the component itself
    });

  return (
    <SearchOrganizationBase
      {...props}
      group={group}
      name={name}
      defaultValue={defaultValue}
      useDialog={useDialog}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
