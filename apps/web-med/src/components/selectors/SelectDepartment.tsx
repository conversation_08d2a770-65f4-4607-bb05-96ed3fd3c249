"use client";

import { use<PERSON>allback, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type {
  SearchValueProps as CoreSearchValueProps,
  SelectValueProps as CoreSelectValueProps,
  SelectValueFieldProps,
} from "@axa/ui/selectors/SelectValue";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchValue,
  SelectValue,
  SelectValueField,
} from "@axa/ui/selectors/SelectValue";

import type { DepartmentValue } from "@/hooks/selectors/use-select-department";

import { useSelectDepartment } from "@/hooks/selectors/use-select-department";

import DepartmentTypeBadge from "../common/DepartmentType";

const i18n = {
  en: {
    label: "Department",
    description: "Select a department",
    placeholder: "Select a department",
  },
};

// Re-export types for backward compatibility
export type DepartmentStructure = DepartmentValue;

/**
 * Base component props for department selector
 */
export interface SelectDepartmentProps
  extends Omit<CoreSelectValueProps<DepartmentValue>, "data" | "type"> {
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter departments by facility ID */
  facilityId?: string;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected department */
  defaultSelection?: DepartmentValue;
  /** Default options to pre-populate */
  defaultOptions?: DepartmentValue[];
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Search group for integration */
  group?: string;
  /** Field label (default: "Department") */
  label?: string;
  /** Placeholder text (default: "Select a department") */
  placeholder?: string;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Department selector component using Core UI SelectValue with type="DEPARTMENT"
 */
export function SelectDepartment({
  enabled = true,
  loading = false,
  useDialog = false,
  children,
  onSelect,
  facilityId,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  group,
  label = i18n.en.label,
  placeholder = i18n.en.placeholder,
  size = "lg",
  className,
  ...props
}: SelectDepartmentProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
    refetch,
  } = useSelectDepartment({
    enabled,
    facilityId,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    group,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const departments = [...data];
    if (
      selection &&
      !departments.find((department) => department.id === selection.id)
    ) {
      departments.unshift(selection);
    }
    return departments;
  }, [data, selection]);

  return (
    <SelectValue<DepartmentValue>
      useDialog={useDialog}
      size={size}
      className={cn(
        {
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
          "min-h-8 py-2": size === "lg",
        },
        className,
      )}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
    >
      {children}
    </SelectValue>
  );
}

/**
 * Form field component props for department selector
 */
export interface SelectDepartmentFieldProps
  extends Omit<SelectValueFieldProps<DepartmentValue>, "data" | "type"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter departments by facility ID */
  facilityId?: string;
  /** Default options to pre-populate */
  defaultOptions?: DepartmentValue[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Field name in form */
  name?: string;
  /** Field label (default: "Department") */
  label?: string;
  /** Field description (default: "Select a department") */
  description?: string;
  /** Placeholder text (default: "Select a department") */
  placeholder?: string;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for department selector
 */
export function SelectDepartmentField({
  enabled = true,
  loading,
  facilityId,
  defaultOptions = [],
  pageSize = 5,
  name = "department",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  size = "lg",
  className,
  ...props
}: SelectDepartmentFieldProps) {
  const {
    data,
    loading: hookLoading,
    setQuery,
    refetch,
  } = useSelectDepartment({
    enabled,
    facilityId,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectValueField<DepartmentValue>
      showDescription
      size={size}
      name={name}
      label={label}
      description={description}
      placeholder={placeholder}
      className={className}
      {...props}
      loading={isLoading}
      data={data}
      onValueChange={setQuery}
    />
  );
}

/**
 * Search component props for department selector
 */
export interface SearchDepartmentProps extends SelectDepartmentProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name?: string;
  /** Default value */
  defaultValue?: string;
}

/**
 * Search component for department selector
 */
export function SearchDepartment({
  group,
  name = "department",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchDepartmentProps) {
  const { selection, onClear, onSelectionChange } =
    useSearchValue<DepartmentValue>({
      name,
      group,
      defaultValue,
      data: [], // Will be provided by the component itself
    });

  return (
    <SearchValue<DepartmentValue>
      {...props}
      name={name}
      group={group}
      defaultValue={defaultValue}
      useDialog={useDialog}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
