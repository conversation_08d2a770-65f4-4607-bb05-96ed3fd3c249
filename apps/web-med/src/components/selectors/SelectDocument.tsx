"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import { SelectDocument as SelectDocumentBase } from "@axa/ui/selectors/SelectDocument";

import type { Document } from "@/hooks/selectors/use-select-document";

import { useSelectDocument } from "@/hooks/selectors/use-select-document";

const i18n = {
  en: {
    label: "Document",
    description: "Select a document",
    placeholder: "Select a document",
  },
};

/**
 * Base component props for document selector - now uses Core UI SelectDocument
 */
export interface SelectDocumentProps
  extends Omit<SelectorProps<Document>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter documents by type */
  type?: string;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected document */
  defaultSelection?: Document;
  /** Default options to pre-populate */
  defaultOptions?: Document[];
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination */
  pageSize?: number;
  /** Search group for integration */
  group?: string;
}

/**
 * Document selector component - migrated to use Core UI SelectDocument
 */
export function SelectDocument({
  loading = false,
  enabled = true,
  useDialog = false,
  type,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  group,
  className,
  placeholder = i18n.en.placeholder,
  onSelect,
  ...props
}: SelectDocumentProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectDocument({
    enabled,
    type,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    group,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectDocumentBase<Document>
      {...props}
      useDialog={useDialog}
      className={className}
      placeholder={placeholder}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query} // ✅ ONLY for query string
      selection={selection} // ✅ Current selection
      onValueChange={setQuery} // ✅ ONLY for query changes
      onSelect={setSelection} // ✅ For selection changes
      // ❌ NO defaultValue prop - this was the critical fix
    />
  );
}

/**
 * Form field props for document selector
 */
export interface SelectDocumentFieldProps
  extends Omit<SelectDocumentProps, "defaultSelection"> {
  /** Field name in form */
  name?: string;
  /** Field label (default: "Document") */
  label?: string;
  /** Field description (default: "Select a document") */
  description?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
}

/**
 * Form field component for document selector - updated to use Core UI pattern
 */
export function SelectDocumentField({
  type,
  enabled = true,
  loading,
  name = "document",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  className,
  defaultOptions = [],
  pageSize = 5,
  ...props
}: SelectDocumentFieldProps) {
  return (
    <FormField
      control={useFormContext().control}
      name={name}
      render={({ field }) => {
        // Find the selected document from the current field value
        const selectedDocument = defaultOptions.find(
          (doc) => doc.id === field.value,
        );

        return (
          <FormItem>
            <FormLabel
              className={cn({
                "sr-only": !showLabel,
              })}
            >
              {label}
            </FormLabel>
            <FormDescription
              className={cn({
                "sr-only": !showDescription,
              })}
            >
              {description}
            </FormDescription>
            <FormControl>
              <SelectDocument
                {...props}
                type={type}
                enabled={enabled}
                loading={loading}
                placeholder={placeholder}
                className={className}
                defaultOptions={defaultOptions}
                pageSize={pageSize}
                defaultSelection={selectedDocument} // ✅ Use defaultSelection for form value
                onSelect={(document) => {
                  field.onChange(document.id); // Store ID in form
                }}
                // ❌ NO value prop - let the component manage its own query
                // ❌ NO defaultValue prop
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

/**
 * Search component props for document selector
 */
export interface SearchDocumentProps
  extends Omit<SelectDocumentProps, "defaultQuery" | "defaultSelection"> {
  /** Search group identifier */
  group: string;
  /** Field name */
  name: string;
  /** Default value (maps to defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for document selector - updated to follow correct pattern
 */
export function SearchDocument({
  group,
  name = "document",
  defaultValue, // This becomes defaultQuery
  onSelect,
  useDialog = false,
  ...props
}: SearchDocumentProps) {
  const { selection, onClear, onSelectionChange } = useSearchValue<Document>({
    name,
    group,
    defaultValue,
    data: [], // Will be provided by the component itself
  });

  return (
    <SelectDocument
      {...props}
      defaultQuery={defaultValue} // ✅ Map to defaultQuery
      useDialog={useDialog}
      defaultSelection={selection ?? undefined} // ✅ Handle null to undefined conversion
      onSelect={onSelectionChange} // ✅ From search hook
      onClear={onClear} // ✅ From search hook
      group={group} // ✅ Pass group for search integration
      // ❌ NO defaultValue prop
      // ❌ NO value prop
    />
  );
}
