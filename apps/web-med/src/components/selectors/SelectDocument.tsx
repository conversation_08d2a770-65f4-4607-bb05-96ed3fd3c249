"use client";

import { useCallback } from "react";
import { useFormContext } from "react-hook-form";

import type {
  SearchDocumentProps as SearchDocumentBaseProps,
  SelectDocumentProps as SelectDocumentBaseProps,
  SelectDocumentFieldProps as SelectDocumentFieldBaseProps,
} from "@axa/ui/selectors/SelectDocument";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchDocument as SearchDocumentBase,
  SelectDocument as SelectDocumentBase,
  SelectDocumentField as SelectDocumentFieldBase,
} from "@axa/ui/selectors/SelectDocument";

import type { Document } from "@/hooks/selectors/use-select-document";

import { useSelectDocument } from "@/hooks/selectors/use-select-document";

const i18n = {
  en: {
    label: "Document",
    description: "Select a document",
    placeholder: "Select a document",
  },
};

/**
 * Document selector component props
 */
export interface SelectDocumentProps
  extends Omit<SelectDocumentBaseProps<Document>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter documents by type */
  type?: string;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected document */
  defaultSelection?: Document;
  /** Default options to pre-populate */
  defaultOptions?: Document[];
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination */
  pageSize?: number;
  /** Search group for integration */
  group?: string;
}

/**
 * Document selector component - uses pre-built SelectDocument with data fetching
 */
export function SelectDocument({
  loading = false,
  enabled = true,
  type,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  group,
  onSelect,
  ...props
}: SelectDocumentProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectDocument({
    enabled,
    type,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectDocumentBase<Document>
      {...props}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

/**
 * Form field props for document selector
 */
export interface SelectDocumentFieldProps
  extends Omit<SelectDocumentFieldBaseProps<Document>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter documents by type */
  type?: string;
  /** Default options to pre-populate */
  defaultOptions?: Document[];
  /** Page size for pagination */
  pageSize?: number;
  /** Field name in form */
  name?: string;
  /** Field label (default: "Document") */
  label?: string;
  /** Field description (default: "Select a document") */
  description?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
}

/**
 * Form field component for document selector - uses pre-built SelectDocumentField with data fetching
 */
export function SelectDocumentField({
  type,
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  name = "documentId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectDocumentFieldProps) {
  const { data, loading: hookLoading } = useSelectDocument({
    enabled,
    type,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectDocumentFieldBase<Document>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={data}
      loading={isLoading}
      onSelect={onSelect}
    />
  );
}

/**
 * Search component props for document selector
 */
export interface SearchDocumentProps
  extends Omit<SearchDocumentBaseProps<Document>, "data"> {
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter documents by type */
  type?: string;
  /** Default options to pre-populate */
  defaultOptions?: Document[];
  /** Page size for pagination */
  pageSize?: number;
  /** Search group identifier */
  group?: string;
  /** Field name */
  name?: string;
  /** Default value */
  defaultValue?: string;
}

/**
 * Search component for document selector - uses pre-built SearchDocument with data fetching
 */
export function SearchDocument({
  enabled = true,
  type,
  defaultOptions = [],
  pageSize = 5,
  group,
  name = "document",
  defaultValue,
  onSelect,
  ...props
}: SearchDocumentProps) {
  const { data, loading: hookLoading } = useSelectDocument({
    enabled,
    type,
    defaultOptions,
    pageSize,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Document>({
    name,
    group,
    defaultValue,
    data, // Pass actual data from hook
  });

  const handleSelect = useCallback(
    async (document: Document) => {
      await onSelect?.(document);
      onSelectionChange(document);
    },
    [onSelect, onSelectionChange],
  );

  return (
    <SearchDocumentBase<Document>
      {...props}
      name={name}
      group={group}
      defaultValue={defaultValue}
      data={data}
      loading={hookLoading}
      onSelect={handleSelect}
    />
  );
}
