"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Contract } from "@/hooks/selectors/use-select-contract";

import { useSelectContract } from "@/hooks/selectors/use-select-contract";

const i18n = {
  en: {
    label: "Contract",
    description: "Select a contract",
    placeholder: "Select a contract",
  },
};

/**
 * Base component props for contract selector
 */
export interface SelectContractProps
  extends Omit<SelectorProps<Contract>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter contracts by organization ID */
  organizationId?: string;
  /** Filter contracts by provider ID */
  providerId?: string;
  /** Filter contracts by status */
  status?: "PENDING" | "REJECTED" | "DRAFT" | "EXPIRED" | "SIGNED";
  /** Filter contracts by type */
  type?:
    | "OTHER"
    | "EMPLOYMENT"
    | "NON_COMPETE"
    | "NON_DISCLOSURE"
    | "SERVICE_RATE"
    | "SERVICE_AGREEMENT";
  /** Include provider data */
  includeProvider?: boolean;
  /** Include organization data */
  includeOrganization?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected contract */
  defaultSelection?: Contract;
  /** Default options to pre-populate */
  defaultOptions?: Contract[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Contract selector component
 */
export function SelectContract({
  loading = false,
  enabled = true,
  useDialog = false,
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  defaultValue,
  children,
  onSelect,
  defaultQuery = "",
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectContractProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectContract({
    enabled,
    organizationId,
    providerId,
    status,
    type,
    includeProvider,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery, // Map defaultValue to defaultQuery for compatibility
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  // Enhanced data is now handled by the hook
  const enhancedData = useMemo(() => {
    const contracts = [...data];
    if (
      selection &&
      !contracts.find((contract) => contract.id === selection.id)
    ) {
      contracts.unshift(selection);
    }
    return contracts;
  }, [data, selection]);

  const handleRenderValue = useCallback((contract: Contract) => {
    return contract.title || i18n.en.placeholder;
  }, []);

  const handleRenderItem = useCallback((contract: Contract) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{contract.title}</div>
        <div className="text-sm text-muted-foreground">
          Status: {contract.status}
        </div>
        {contract.organization?.name && (
          <div className="text-xs text-muted-foreground">
            Organization: {contract.organization.name}
          </div>
        )}
        {contract.provider?.person?.firstName && (
          <div className="text-xs text-muted-foreground">
            Provider: {contract.provider.person.firstName}{" "}
            {contract.provider.person.lastName}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Contract>
      useDialog={useDialog}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      {...props}
      loading={isLoading}
      placeholder={placeholder}
      label={placeholder}
      data={enhancedData}
      open={open}
      onOpenChange={setOpen}
      value={query} // ✅ ONLY for query string
      selection={selection ?? undefined} // ✅ Current selection
      onValueChange={setQuery} // ✅ ONLY for query changes
      onSelect={setSelection} // ✅ For selection changes
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
      // ❌ NO defaultValue prop here - this was the critical bug
    >
      {children}
    </Selector>
  );
}

/**
 * Form field props for contract selector
 */
export interface SelectContractFieldProps
  extends Omit<SelectorProps<Contract>, "data" | "onValueChange"> {
  /** Field name in form */
  name?: string;
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Field label (default: "Contract") */
  label?: string;
  /** Field description (default: "Select a contract") */
  description?: string;
  /** Placeholder text (default: "Select a contract") */
  placeholder?: string;
  /** Filter contracts by organization ID */
  organizationId?: string;
  /** Filter contracts by provider ID */
  providerId?: string;
  /** Filter contracts by status */
  status?: "PENDING" | "REJECTED" | "DRAFT" | "EXPIRED" | "SIGNED";
  /** Filter contracts by type */
  type?:
    | "OTHER"
    | "EMPLOYMENT"
    | "NON_COMPETE"
    | "NON_DISCLOSURE"
    | "SERVICE_RATE"
    | "SERVICE_AGREEMENT";
  /** Include provider data */
  includeProvider?: boolean;
  /** Include organization data */
  includeOrganization?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Contract[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for contract selector
 */
export function SelectContractField({
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  defaultOptions,
  pageSize = 5,
  enabled = true,
  loading,
  name = "contract",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  size = "lg",
  className,
  ...props
}: SelectContractFieldProps) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectContract({
    enabled,
    organizationId,
    providerId,
    status,
    type,
    includeProvider,
    includeOrganization,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <FormField
      control={useFormContext().control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectContract
              {...props}
              placeholder={placeholder}
              size={size}
              className={className}
              loading={isLoading}
              organizationId={organizationId}
              providerId={providerId}
              status={status}
              type={type}
              includeProvider={includeProvider}
              includeOrganization={includeOrganization}
              defaultOptions={defaultOptions}
              pageSize={pageSize}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
              onValueChange={setQuery}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for contract selector
 */
export interface SearchContractProps extends SelectContractProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name: string;
  /** Default value (becomes defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for contract selector
 */
export function SearchContract({
  group,
  name = "contract",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchContractProps) {
  const { selection, onClear, onSelectionChange } = useSearchValue<Contract>({
    name,
    group,
    defaultValue,
    data: [], // Will be provided by the component itself
  });

  return (
    <SelectContract
      {...props}
      defaultQuery={defaultValue} // ✅ Map defaultValue to defaultQuery for search pattern
      useDialog={useDialog}
      selection={selection} // ✅ From search hook
      onSelect={onSelectionChange ?? onSelect} // ✅ From search hook
      onClear={onClear} // ✅ From search hook
    />
  );
}
