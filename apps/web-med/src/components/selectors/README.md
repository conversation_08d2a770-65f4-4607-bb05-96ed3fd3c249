# Selector Components Refactoring Plan

This document outlines the standardization and refactoring plan for all selector components in the web-med application.

## Core Selector Pattern: SelectSpecialty

`SelectSpecialty` serves as our reference implementation for all custom selectors using the **Core Selector Pattern**. This pattern separates data management from presentation logic.

### Key Principles

1. **Core Selector Pattern**: Separate presentation logic (core) from data management (wrappers)
2. **Prop-Driven Core**: Core components receive all data and state via props - no hooks inside
3. **Data Coordination**: Search components use both selector and search hooks, coordinating between them
4. **Type Safety**: Use `RouterOutputs` types exclusively, no custom type definitions
5. **Selective Data Properties**: Only select and expose properties needed for rendering/representation in the selector
6. **Use Pre-built Types**: Import and use types from pre-built components when available
7. **Four Component Architecture**: Each custom selector follows this structure:
   - `SelectEntityCore` - Pure presentation component (prop-driven)
   - `SelectEntity` - Standard data wrapper (uses hook, passes to core)
   - `SelectEntityField` - Form integration wrapper (uses SelectEntity)
     - `SearchEntity` - Search + data coordination wrapper (uses both hooks, passes to core)

### Core Selector Architecture

The **Core Selector Pattern** ensures complete separation of concerns between data management and presentation logic:

#### 1. SelectEntityCore (Pure Presentation)

```typescript
function SelectEntityCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  // ...other props
}: SelectEntityCoreProps) {
  // Pure presentation logic only
  // No hooks, no data fetching
  // Receives everything via props
}
```

#### 2. SelectEntity (Standard Data Wrapper)

```typescript
export function SelectEntity(props: SelectEntityProps) {
  const {
    data,
    loading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectEntity({
    enabled: props.enabled,
    defaultQuery: props.defaultValue ?? props.defaultQuery,
    // ...other hook props
  });

  return (
    <SelectEntityCore
      {...props}
      data={data}
      loading={loading}
      selection={selection}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}
```

#### 3. SearchEntity (Dual Hook Coordination)

```typescript
export function SearchEntity(props: SearchEntityProps) {
  // Hook 1: Data fetching and core selection state
  const {
    data,
    loading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectEntity({
    enabled: props.enabled,
    defaultQuery: props.defaultValue ?? props.defaultQuery,
    // ...other hook props
  });

  // Hook 2: URL search params (receives data from Hook 1)
  const { selection, onClear, onSelectionChange } = useSearchValue({
    name: props.name,
    group: props.group,
    defaultValue: props.defaultValue,
    data: data ?? [], // ← Data flows from selector hook to search hook
  });

  // Coordinate between both hooks
  const handleSelect = useCallback(
    async (entity: Entity) => {
      await setSelection(entity);           // Update core state
      await props.onSelect?.(entity);       // Notify parent
      onSelectionChange(entity);            // Update URL params
    },
    [setSelection, onSelectionChange, props.onSelect],
  );

  return (
    <SelectEntityCore
      {...props}
      data={data}
      loading={loading}
      selection={selection ?? undefined}   // ← From search hook
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}              // ← Coordinated handler
      onClear={onClear}                    // ← From search hook
    />
  );
}
```

#### 4. SelectEntityField (Form Integration)

```typescript
export function SelectEntityField(props: SelectEntityFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={props.name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{props.label}</FormLabel>
          <FormControl>
            <SelectEntity
              {...props}
              value={field.value}
              onSelect={(entity) => field.onChange(entity.id)}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
```

### Key Benefits of This Pattern

- **Zero Code Duplication**: All presentation logic lives in one core component
- **Proper Data Flow**: Search functionality receives data from selector hook
- **Clear Separation**: Data concerns are completely separate from UI concerns
- **Consistent Interface**: All variants use the same core rendering logic
- **Easy Testing**: Core components are pure and easily testable
- **Maintainable**: Changes to core behavior only need to be made in one place

### Default Props to Remove

These props should be moved from component interfaces to internal hook defaults:

- `defaultQuery` → Handle internally in hook (default: `""`)
- `defaultSelection` → Handle internally in hook (default: `null`)
- `defaultDebounce` → Handle internally in hook (default: `500`)
- `defaultOpen` → Handle internally in hook (default: `false`)

## Complete Selector Component List

All selector components in `apps/web-med/src/components/selectors/`:

### ✅ **Completed - Reference Implementations**

These selectors follow the **Core Selector Pattern** and serve as reference implementations:

- [x] **SelectSpecialty** - Complete Core Selector Pattern implementation
- [x] **SelectMedicalRole** - Complete Core Selector Pattern implementation

### 🏗️ **Pre-built Components**

These selectors use or should migrate to existing components from `packages/ui/src/selectors`:

- [ ] **SelectOrganization** - Already uses `@axa/ui/selectors/SelectOrganization` ✅
- [ ] **SelectPerson** - Migrate to `@axa/ui/selectors/SelectPerson`
- [ ] **SelectDocument** - Migrate to `@axa/ui/selectors/SelectDocument`
- [ ] **SelectAddress** - Migrate to `@axa/ui/selectors/SelectLocation`
- [ ] **SelectFacility** - Migrate to `@axa/ui/selectors/SelectLocation`

### 🔧 **Custom Selectors - Core Selector Pattern Needed**

These selectors need refactoring to follow the **Core Selector Pattern**:

**High Priority:**

- [ ] **SelectProvider** - Complex includes logic, custom filtering
- [ ] **SelectContract** - Complex includes, multiple filter types
- [ ] **SelectPosition** - Complex includes, multiple filters

**Medium Priority:**

- [ ] **SelectShift** - Complex includes, custom filtering
- [ ] **SelectApplication** - Complex includes, multiple filters
- [ ] **SelectJobPost** - Missing pagination props, inconsistent return type
- [ ] **SelectDepartment** - Uses Values API, custom type mapping

**Low Priority:**

- [ ] **SelectQualification** - Simple structure, missing some props
- [ ] **SelectIncident** - Missing pagination props, inconsistent interface
- [ ] **SelectContact** - Missing pagination props, inconsistent return
- [ ] **SelectOffer** - Missing pagination props, simple structure

## Standard Hook Interface

```typescript
export interface UseSelectEntityProps {
  enabled?: boolean;
  organizationId?: string;
  pageNumber?: number;
  pageSize?: number;
  onSelect?: (entity: Entity) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export interface UseSelectEntityReturn {
  data: Entity[] | undefined;
  loading: boolean;
  selection: Entity | null;
  open: boolean;
  query: string;
  setSelection: (entity: Entity) => Promise<void>;
  setQuery: (value: string) => Promise<void>;
  setOpen: (open: boolean) => void;
  refetch: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  totalCount: number | undefined;
}
```

## Standard Component Interface

```typescript
export interface SelectEntityProps {
  open?: boolean;
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  useDialog?: boolean;
  onSelect?: (entity: Entity) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export interface SelectEntityFieldProps extends SelectEntityProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export interface SearchEntityProps extends SelectEntityProps {
  group: string;
  name: string;
  defaultValue?: string;
}
```

## Implementation Plan

### ✅ **Phase 1: Reference Implementation Complete**

1. ✅ **SelectSpecialty** - Core Selector Pattern reference implementation complete
   - ✅ `SelectSpecialtyCore` - Pure prop-driven component
   - ✅ `SelectSpecialty` - Standard data wrapper
   - ✅ `SearchSpecialty` - Dual hook coordination (selector + search)
   - ✅ `SelectSpecialtyField` - Form integration wrapper
2. ✅ **SelectMedicalRole** - Core Selector Pattern implementation complete
   - ✅ `SelectMedicalRoleCore` - Pure prop-driven component
   - ✅ `SelectMedicalRole` - Standard data wrapper
   - ✅ `SearchMedicalRole` - Dual hook coordination (selector + search)
   - ✅ `SelectMedicalRoleField` - Form integration wrapper

### 📦 **Phase 2: Pre-built Component Migration**

3. 🔄 **SelectPerson** - Replace with `@axa/ui/selectors/SelectPerson`
4. 🔄 **SelectDocument** - Replace with `@axa/ui/selectors/SelectDocument`
5. 🔄 **SelectAddress** - Replace with `@axa/ui/selectors/SelectLocation`
6. 🔄 **SelectFacility** - Replace with `@axa/ui/selectors/SelectLocation`

### 🔧 **Phase 3: High Priority Core Selector Pattern**

7. 🔄 **SelectProvider** - Complex includes logic, used frequently
8. 🔄 **SelectContract** - Complex includes, business critical
9. 🔄 **SelectPosition** - Complex includes, used frequently

### 🔧 **Phase 4: Medium Priority Core Selector Pattern**

10. 🔄 **SelectShift** - Complex includes, custom filtering
11. 🔄 **SelectApplication** - Complex includes, multiple filters
12. 🔄 **SelectJobPost** - Missing pagination props
13. 🔄 **SelectDepartment** - Values API, custom type mapping

### 🔧 **Phase 5: Low Priority Core Selector Pattern**

14. 🔄 **SelectQualification** - Simple structure, missing props
15. 🔄 **SelectIncident** - Missing pagination props
16. 🔄 **SelectContact** - Missing pagination props
17. 🔄 **SelectOffer** - Missing pagination props

## Progress Summary

**Total Selectors**: 19  
**✅ Completed**: 2 (SelectSpecialty, SelectMedicalRole)  
**🔄 Remaining**: 17

- **Reference Implementations**: 2/2 ✅ Complete
- **Pre-built Migration**: 0/4 remaining
- **Custom Pattern Application**: 0/13 remaining

```mermaid
---
title: Selector Refactoring Progress
---
pie title Selector Component Status
    "✅ Completed (Core Pattern)" : 2
    "🏗️ Pre-built Migration" : 4
    "🔧 Custom Pattern Needed" : 13
```

### Completion Breakdown

```mermaid
---
title: Implementation Strategy
---
graph TD
    A["19 Total Selectors"] --> B["✅ 2 Reference<br/>Implementations<br/>(SelectSpecialty,<br/>SelectMedicalRole)"]
    A --> C["🏗️ 4 Pre-built<br/>Component Migration<br/>(SelectPerson,<br/>SelectDocument, etc.)"]
    A --> D["🔧 13 Core Selector<br/>Pattern Application<br/>(High/Medium/Low Priority)"]

    B --> E["Complete Pattern<br/>Examples"]
    C --> F["Simplified<br/>Implementation"]
    D --> G["Systematic<br/>Refactoring"]

    style B fill:#d4edda
    style C fill:#fff3cd
    style D fill:#f8d7da
```

## Validation Checklist

For each refactored selector, verify:

**Core Selector Pattern:**

- [ ] Has `SelectEntityCore` component that is purely prop-driven (no hooks)
- [ ] Has `SelectEntity` wrapper that uses hook and passes all data to core
- [ ] Has `SearchEntity` wrapper that coordinates both selector and search hooks
- [ ] Has `SelectEntityField` wrapper for form integration
- [ ] Search component passes data from selector hook to search hook via `data: data ?? []`

**Technical Requirements:**

- [ ] Uses `RouterOutputs` types exclusively
- [ ] Removes all "default" props from component interfaces
- [ ] Hook handles defaults internally
- [ ] Implements standard hook return interface
- [ ] Maintains all existing functionality
- [ ] Has consistent prop naming
- [ ] Includes proper TypeScript types
- [ ] Core component receives all state via props
- [ ] Search coordination works correctly (updates both hooks)

## Notes

- **✅ Reference Implementations**: SelectSpecialty and SelectMedicalRole now serve as the definitive examples of the Core Selector Pattern
- **SelectOrganization**: Already uses pre-built component correctly
- **Pre-built Migration**: Four selectors will be simplified by using existing `@axa/ui/selectors` components
- **Core Selector Pattern**: All custom selectors will follow the 4-component architecture (Core, Standard, Field, Search)
- **Data Flow**: Search components must pass data from selector hook to search hook via `data: data ?? []`
- **Type Safety**: Use `RouterOutputs` types exclusively, no custom type definitions
- **Selective Properties**: Only expose properties actually used for rendering (id, name, key display fields)
- **Zero Duplication**: All presentation logic lives in the Core component to eliminate code duplication
- **Hook Coordination**: Search components coordinate between both `useSelectEntity` and `useSearchValue` hooks
