"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";

import type {
  SearchLocationProps as BaseSearchLocationProps,
  SelectLocationFieldProps as SelectFieldProps,
  SelectLocationProps as SelectorProps,
} from "@axa/ui/selectors/SelectLocation";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchLocation,
  SelectLocation,
  SelectLocationField,
} from "@axa/ui/selectors/SelectLocation";

import type {
  Address,
  AddressAsLocation,
} from "@/hooks/selectors/use-select-address";

import { useSelectAddress } from "@/hooks/selectors/use-select-address";

const i18n = {
  en: {
    label: "Address",
    description: "Select an address",
    placeholder: "Select an address",
  },
};

function ensureAddressAsLocation(
  address: Address | AddressAsLocation,
): AddressAsLocation {
  if ("name" in address && "address" in address) {
    return address;
  }

  return {
    id: address.id,
    name: address.location?.name || address.formatted || "Unknown Location",
    address: {
      formatted: address.formatted,
      street: address.street,
      city: address.city,
      state: address.state,
      postal: address.postal,
      country: address.country,
    },
  };
}

function ensureAddressAsLocationArray(
  addresses: (Address | AddressAsLocation)[],
): AddressAsLocation[] {
  return addresses.map(ensureAddressAsLocation);
}

export interface SelectAddressProps
  extends Omit<
    SelectorProps<AddressAsLocation>,
    "data" | "onSelect" | "selection" | "defaultSelection"
  > {
  loading?: boolean;
  enabled?: boolean;
  defaultQuery?: string;
  defaultSelection?: Address | AddressAsLocation;
  defaultOptions?: Address[] | AddressAsLocation[];
  defaultDebounce?: number;
  pageSize?: number;
  size?: "sm" | "md" | "lg";
  onSelect?: (address: Address | AddressAsLocation) => void | Promise<void>;
}

export function SelectAddress({
  loading = false,
  enabled = true,
  useDialog = false,
  children,
  onSelect,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectAddressProps) {
  const transformedDefaultSelection = defaultSelection
    ? ensureAddressAsLocation(defaultSelection)
    : undefined;
  const transformedDefaultOptions =
    ensureAddressAsLocationArray(defaultOptions);

  const handleSelect = useCallback(
    async (address: AddressAsLocation) => {
      if (onSelect) {
        await onSelect(address);
      }
    },
    [onSelect],
  );

  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectAddress({
    enabled,
    defaultQuery,
    defaultSelection: transformedDefaultSelection,
    defaultOptions: transformedDefaultOptions,
    defaultDebounce,
    pageSize,
    onSelect: handleSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const addresses = [...data];
    if (
      selection &&
      !addresses.find((address) => address.id === selection.id)
    ) {
      addresses.unshift(selection);
    }
    return addresses;
  }, [data, selection]);

  return (
    <SelectLocation<AddressAsLocation>
      useDialog={useDialog}
      size={size}
      className={className}
      {...props}
      data={enhancedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
      placeholder={placeholder}
    >
      {children}
    </SelectLocation>
  );
}

export interface SelectAddressFieldProps<
  T extends AddressAsLocation = AddressAsLocation,
> extends Omit<SelectFieldProps<T>, "data" | "onValueChange"> {
  loading?: boolean;
  enabled?: boolean;
  defaultOptions?: AddressAsLocation[];
  pageSize?: number;
  label?: string;
  description?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
}

export function SelectAddressField<
  T extends AddressAsLocation = AddressAsLocation,
>({
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  size = "md",
  className,
  ...props
}: SelectAddressFieldProps<T>) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectAddress({
    enabled,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectLocationField
      label={label}
      description={description}
      placeholder={placeholder}
      size={size}
      className={className}
      {...props}
      loading={isLoading}
      data={data as unknown as T[]}
      onValueChange={setQuery}
    />
  );
}

export interface SearchAddressProps extends SelectAddressProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchAddress({
  group,
  name = "address",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchAddressProps) {
  const { selection, onClear, onSelectionChange } =
    useSearchValue<AddressAsLocation>({
      name,
      group,
      defaultValue,
      data: [],
    });

  return (
    <SearchLocation
      {...props}
      group={group}
      name={name}
      defaultValue={defaultValue}
      useDialog={useDialog}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
