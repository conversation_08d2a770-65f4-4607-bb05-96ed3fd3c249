"use client";

import { useCallback } from "react";

import type {
  SearchLocationProps as SearchLocationBaseProps,
  SelectLocationProps as SelectLocationBaseProps,
  SelectLocationFieldProps as SelectLocationFieldBaseProps,
} from "@axa/ui/selectors/SelectLocation";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchLocation as SearchLocationBase,
  SelectLocation as SelectLocationBase,
  SelectLocationField as SelectLocationFieldBase,
} from "@axa/ui/selectors/SelectLocation";

import type {
  Address,
  AddressAsLocation,
} from "@/hooks/selectors/use-select-address";

import { useSelectAddress } from "@/hooks/selectors/use-select-address";

// Re-export types for backward compatibility
export type { Address, AddressAsLocation };

const i18n = {
  en: {
    label: "Address",
    description: "Select an address",
    placeholder: "Select an address",
  },
};

/**
 * Address selector component props
 */
export interface SelectAddressProps
  extends Omit<SelectLocationBaseProps<AddressAsLocation>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected address */
  defaultSelection?: Address | AddressAsLocation;
  /** Default options to pre-populate */
  defaultOptions?: Address[] | AddressAsLocation[];
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination */
  pageSize?: number;
  /** Search group for integration */
  group?: string;
  /** Callback when address is selected */
  onSelect?: (address: Address | AddressAsLocation) => void | Promise<void>;
}

/**
 * Address selector component - uses pre-built SelectLocation with data fetching
 */
export function SelectAddress({
  loading = false,
  enabled = true,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  group,
  onSelect,
  ...props
}: SelectAddressProps) {
  // Transform legacy props to match hook's expected format
  const transformedDefaultSelection = defaultSelection
    ? "name" in defaultSelection && "address" in defaultSelection
      ? defaultSelection
      : {
          id: defaultSelection.id,
          name:
            defaultSelection.location?.name ||
            defaultSelection.formatted ||
            "Unknown Location",
          address: {
            formatted: defaultSelection.formatted,
            street: defaultSelection.street,
            city: defaultSelection.city,
            state: defaultSelection.state,
            postal: defaultSelection.postal,
            country: defaultSelection.country,
          },
        }
    : undefined;

  const transformedDefaultOptions = defaultOptions.map((option) =>
    "name" in option && "address" in option
      ? option
      : {
          id: option.id,
          name: option.location?.name || option.formatted || "Unknown Location",
          address: {
            formatted: option.formatted,
            street: option.street,
            city: option.city,
            state: option.state,
            postal: option.postal,
            country: option.country,
          },
        },
  );

  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectAddress({
    enabled,
    defaultQuery,
    defaultSelection: transformedDefaultSelection,
    defaultOptions: transformedDefaultOptions,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectLocationBase<AddressAsLocation>
      {...props}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

/**
 * Form field props for address selector
 */
export interface SelectAddressFieldProps
  extends Omit<SelectLocationFieldBaseProps<AddressAsLocation>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Address[] | AddressAsLocation[];
  /** Page size for pagination */
  pageSize?: number;
  /** Field name in form */
  name?: string;
  /** Field label (default: "Address") */
  label?: string;
  /** Field description (default: "Select an address") */
  description?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
}

/**
 * Form field component for address selector - uses pre-built SelectLocationField with data fetching
 */
export function SelectAddressField({
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  name = "addressId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectAddressFieldProps) {
  // Transform legacy props to match hook's expected format
  const transformedDefaultOptions = defaultOptions.map((option) =>
    "name" in option && "address" in option
      ? option
      : {
          id: option.id,
          name: option.location?.name || option.formatted || "Unknown Location",
          address: {
            formatted: option.formatted,
            street: option.street,
            city: option.city,
            state: option.state,
            postal: option.postal,
            country: option.country,
          },
        },
  );

  const { data, loading: hookLoading } = useSelectAddress({
    enabled,
    defaultOptions: transformedDefaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectLocationFieldBase<AddressAsLocation>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={data}
      loading={isLoading}
      onSelect={onSelect}
    />
  );
}

/**
 * Search component props for address selector
 */
export interface SearchAddressProps
  extends Omit<SearchLocationBaseProps<AddressAsLocation>, "data"> {
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Address[] | AddressAsLocation[];
  /** Page size for pagination */
  pageSize?: number;
  /** Search group identifier */
  group: string;
  /** Field name */
  name?: string;
  /** Default value */
  defaultValue?: string;
  /** Callback when address is selected */
  onSelect?: (address: Address | AddressAsLocation) => void | Promise<void>;
}

/**
 * Search component for address selector - uses pre-built SearchLocation with data fetching
 * FIXED: Now properly passes hook data instead of empty array
 */
export function SearchAddress({
  enabled = true,
  defaultOptions = [],
  pageSize = 5,
  group,
  name = "address",
  defaultValue,
  onSelect,
  ...props
}: SearchAddressProps) {
  // Transform legacy props to match hook's expected format
  const transformedDefaultOptions = defaultOptions.map((option) =>
    "name" in option && "address" in option
      ? option
      : {
          id: option.id,
          name: option.location?.name || option.formatted || "Unknown Location",
          address: {
            formatted: option.formatted,
            street: option.street,
            city: option.city,
            state: option.state,
            postal: option.postal,
            country: option.country,
          },
        },
  );

  const { data, loading: hookLoading } = useSelectAddress({
    enabled,
    defaultOptions: transformedDefaultOptions,
    pageSize,
  });

  const { selection, onClear, onSelectionChange } =
    useSearchValue<AddressAsLocation>({
      name,
      group,
      defaultValue,
      data, // CRITICAL FIX: Pass actual data instead of empty array
    });

  const handleSelect = useCallback(
    async (address: AddressAsLocation) => {
      await onSelect?.(address);
      onSelectionChange(address);
    },
    [onSelect, onSelectionChange],
  );

  return (
    <SearchLocationBase<AddressAsLocation>
      {...props}
      name={name}
      group={group}
      defaultValue={defaultValue}
      data={data}
      loading={hookLoading}
      onSelect={handleSelect}
    />
  );
}
