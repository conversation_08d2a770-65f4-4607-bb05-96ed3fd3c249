"use client";

import { useCallback } from "react";

import type {
  SearchLocationProps as SearchLocationBaseProps,
  SelectLocationProps as SelectLocationBaseProps,
  SelectLocationFieldProps as SelectLocationFieldBaseProps,
} from "@axa/ui/selectors/SelectLocation";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchLocation as SearchLocationBase,
  SelectLocation as SelectLocationBase,
  SelectLocationField as SelectLocationFieldBase,
} from "@axa/ui/selectors/SelectLocation";

import type { Facility } from "@/hooks/selectors/use-select-facility";

import { useSelectFacility } from "@/hooks/selectors/use-select-facility";

// Re-export types for backward compatibility
export type { Facility };
export type FacilityStructure = Facility;

const i18n = {
  en: {
    label: "Facility",
    description: "Select a facility",
    placeholder: "Select a facility",
  },
};

// Transform Facility to match PartialLocation interface
interface FacilityAsLocation {
  id: string;
  name: string;
  address: {
    formatted: string | null;
    street?: string | null;
    city?: string | null;
    state?: string | null;
    postal?: string | null;
    country?: string | null;
  };
  [key: string]: any; // Index signature to satisfy PartialLocation constraint
}

/**
 * Facility selector component props
 */
export interface SelectFacilityProps
  extends Omit<
    SelectLocationBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected facility */
  defaultSelection?: Facility;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination */
  pageSize?: number;
  /** Search group for integration */
  group?: string;
  /** Callback when facility is selected */
  onSelect?: (facility: Facility) => void | Promise<void>;
}

/**
 * Facility selector component - uses pre-built SelectLocation with data fetching
 */
export function SelectFacility({
  loading = false,
  enabled = true,
  organizationId,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  group,
  onSelect,
  ...props
}: SelectFacilityProps) {
  // Transform facility data to match PartialLocation interface
  const transformedDefaultSelection = defaultSelection
    ? {
        id: defaultSelection.id,
        name: defaultSelection.name,
        address: {
          formatted: defaultSelection.address?.formatted ?? null,
          street: defaultSelection.address?.street ?? null,
          city: defaultSelection.address?.city ?? null,
          state: defaultSelection.address?.state ?? null,
          postal: defaultSelection.address?.postal ?? null,
          country: defaultSelection.address?.country ?? null,
        },
      }
    : undefined;

  const transformedDefaultOptions = defaultOptions.map((option) => ({
    id: option.id,
    name: option.name,
    address: {
      formatted: option.address?.formatted ?? null,
      street: option.address?.street ?? null,
      city: option.address?.city ?? null,
      state: option.address?.state ?? null,
      postal: option.address?.postal ?? null,
      country: option.address?.country ?? null,
    },
  }));

  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectFacility({
    enabled,
    organizationId,
    defaultQuery,
    defaultSelection,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  // Transform hook data to match PartialLocation interface
  const transformedData: FacilityAsLocation[] = data.map((facility) => ({
    id: facility.id,
    name: facility.name,
    address: {
      formatted: facility.address?.formatted ?? null,
      street: facility.address?.street ?? null,
      city: facility.address?.city ?? null,
      state: facility.address?.state ?? null,
      postal: facility.address?.postal ?? null,
      country: facility.address?.country ?? null,
    },
  }));

  // Transform selection to match PartialLocation interface
  const transformedSelection = selection
    ? {
        id: selection.id,
        name: selection.name,
        address: {
          formatted: selection.address?.formatted ?? null,
          street: selection.address?.street ?? null,
          city: selection.address?.city ?? null,
          state: selection.address?.state ?? null,
          postal: selection.address?.postal ?? null,
          country: selection.address?.country ?? null,
        },
      }
    : undefined;

  return (
    <SelectLocationBase<FacilityAsLocation>
      {...props}
      data={transformedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={transformedSelection}
      onValueChange={setQuery}
      onSelect={(facilityAsLocation) => {
        // Find the original facility from the hook data
        const originalFacility = data.find(
          (f) => f.id === facilityAsLocation.id,
        );
        if (originalFacility) {
          setSelection(originalFacility);
        }
      }}
    />
  );
}

/**
 * Form field props for facility selector
 */
export interface SelectFacilityFieldProps
  extends Omit<
    SelectLocationFieldBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Page size for pagination */
  pageSize?: number;
  /** Field name in form */
  name?: string;
  /** Field label (default: "Facility") */
  label?: string;
  /** Field description (default: "Select a facility") */
  description?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
  /** Callback when facility is selected */
  onSelect?: (facility: Facility) => void | Promise<void>;
}

/**
 * Form field component for facility selector - uses pre-built SelectLocationField with data fetching
 */
export function SelectFacilityField({
  enabled = true,
  loading,
  organizationId,
  pageSize = 5,
  name = "facilityId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectFacilityFieldProps) {
  const { data, loading: hookLoading } = useSelectFacility({
    enabled,
    organizationId,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  // Transform hook data to match PartialLocation interface
  const transformedData: FacilityAsLocation[] = data.map((facility) => ({
    id: facility.id,
    name: facility.name,
    address: {
      formatted: facility.address?.formatted ?? null,
      street: null,
      city: null,
      state: null,
      postal: null,
      country: null,
    },
  }));

  return (
    <SelectLocationFieldBase<FacilityAsLocation>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={transformedData}
      loading={isLoading}
      onSelect={async (facilityAsLocation: FacilityAsLocation) => {
        // Find the original facility from the hook data
        const originalFacility = data.find(
          (f) => f.id === facilityAsLocation.id,
        );
        if (originalFacility && onSelect) {
          await onSelect(originalFacility);
        }
      }}
    />
  );
}

/**
 * Search component props for facility selector
 */
export interface SearchFacilityProps
  extends Omit<
    SearchLocationBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Page size for pagination */
  pageSize?: number;
  /** Search group identifier */
  group: string;
  /** Field name */
  name?: string;
  /** Default value */
  defaultValue?: string;
  /** Callback when facility is selected */
  onSelect?: (facility: Facility) => void | Promise<void>;
}

/**
 * Search component for facility selector - uses pre-built SearchLocation with data fetching
 * FIXED: Now properly passes hook data instead of empty array
 */
export function SearchFacility({
  enabled = true,
  organizationId,
  pageSize = 5,
  group,
  name = "facility",
  defaultValue,
  onSelect,
  ...props
}: SearchFacilityProps) {
  const { data, loading: hookLoading } = useSelectFacility({
    enabled,
    organizationId,
    pageSize,
  });

  // Transform hook data to match PartialLocation interface
  const transformedData: FacilityAsLocation[] = data.map((facility) => ({
    id: facility.id,
    name: facility.name,
    address: {
      formatted: facility.address?.formatted ?? null,
      street: null,
      city: null,
      state: null,
      postal: null,
      country: null,
    },
  }));

  const { selection, onClear, onSelectionChange } =
    useSearchValue<FacilityAsLocation>({
      name,
      group,
      defaultValue,
      data: transformedData, // CRITICAL FIX: Pass actual data instead of empty array
    });

  const handleSelect = useCallback(
    async (facilityAsLocation: FacilityAsLocation) => {
      // Find the original facility from the hook data
      const originalFacility = data.find((f) => f.id === facilityAsLocation.id);
      if (originalFacility) {
        await onSelect?.(originalFacility);
        onSelectionChange(facilityAsLocation);
      }
    },
    [onSelect, onSelectionChange, data],
  );

  return (
    <SearchLocationBase<FacilityAsLocation>
      {...props}
      name={name}
      group={group}
      defaultValue={defaultValue}
      data={transformedData}
      loading={hookLoading}
      onSelect={handleSelect}
    />
  );
}
