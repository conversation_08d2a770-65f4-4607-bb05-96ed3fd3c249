"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Shift } from "@/hooks/selectors/use-select-shift";

import { useSelectShift } from "@/hooks/selectors/use-select-shift";

const i18n = {
  en: {
    label: "Shift",
    description: "Select a shift",
    placeholder: "Select a shift",
  },
};

/**
 * Valid shift status values
 */
type ShiftStatus =
  | "PENDING"
  | "COMPLETED"
  | "CANCELLED"
  | "ACTIVE"
  | "REJECTED"
  | "APPROVED"
  | "CONFIRMED";

/**
 * Base component props for shift selector
 */
export interface SelectShiftProps extends Omit<SelectorProps<Shift>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter shifts by organization ID */
  organizationId?: string;
  /** Filter shifts by location ID */
  locationId?: string;
  /** Filter shifts by status */
  status?: ShiftStatus[];
  /** Include location data */
  includeLocation?: boolean;
  /** Include provider data */
  includeProvider?: boolean;
  /** Include person data */
  includePerson?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected shift */
  defaultSelection?: Shift;
  /** Default options to pre-populate */
  defaultOptions?: Shift[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Shift selector component
 */
export function SelectShift({
  loading = false,
  enabled = true,
  useDialog = false,
  organizationId,
  locationId,
  status,
  includeLocation = true,
  includeProvider = true,
  includePerson = true,
  defaultValue,
  children,
  onSelect,
  defaultQuery = "",
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectShiftProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectShift({
    enabled,
    organizationId,
    locationId,
    status,
    includeLocation,
    includeProvider,
    includePerson,
    defaultQuery: defaultValue ?? defaultQuery, // Map defaultValue to defaultQuery for compatibility
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  // Enhanced data is now handled by the hook
  const enhancedData = useMemo(() => {
    const shifts = [...data];
    if (selection && !shifts.find((shift) => shift.id === selection.id)) {
      shifts.unshift(selection);
    }
    return shifts;
  }, [data, selection]);

  const handleRenderValue = useCallback((shift: Shift) => {
    return shift.summary || i18n.en.placeholder;
  }, []);

  const handleRenderItem = useCallback((shift: Shift) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{shift.summary}</div>
        {shift.location?.name && (
          <div className="text-sm text-muted-foreground">
            {shift.location.name}
          </div>
        )}
        {shift.provider?.person && (
          <div className="text-xs text-muted-foreground">
            Provider: {shift.provider.person.firstName}{" "}
            {shift.provider.person.lastName}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Shift>
      useDialog={useDialog}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      {...props}
      loading={isLoading}
      placeholder={placeholder}
      label={placeholder}
      data={enhancedData}
      open={open}
      onOpenChange={setOpen}
      value={query} // ✅ ONLY for query string
      selection={selection ?? undefined} // ✅ Current selection
      onValueChange={setQuery} // ✅ ONLY for query changes
      onSelect={setSelection} // ✅ For selection changes
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
      // ❌ NO defaultValue prop here - this was the critical bug
    >
      {children}
    </Selector>
  );
}

/**
 * Form field props for shift selector
 */
export interface SelectShiftFieldProps
  extends Omit<SelectorProps<Shift>, "data" | "onValueChange"> {
  /** Field name in form */
  name?: string;
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Field label (default: "Shift") */
  label?: string;
  /** Field description (default: "Select a shift") */
  description?: string;
  /** Placeholder text (default: "Select a shift") */
  placeholder?: string;
  /** Filter shifts by organization ID */
  organizationId?: string;
  /** Filter shifts by location ID */
  locationId?: string;
  /** Filter shifts by status */
  status?: ShiftStatus[];
  /** Include location data */
  includeLocation?: boolean;
  /** Include provider data */
  includeProvider?: boolean;
  /** Include person data */
  includePerson?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Shift[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for shift selector
 */
export function SelectShiftField({
  organizationId,
  locationId,
  status,
  includeLocation = true,
  includeProvider = true,
  includePerson = true,
  defaultOptions,
  pageSize = 5,
  enabled = true,
  loading,
  name = "shift",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  size = "lg",
  className,
  ...props
}: SelectShiftFieldProps) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectShift({
    enabled,
    organizationId,
    locationId,
    status,
    includeLocation,
    includeProvider,
    includePerson,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <FormField
      control={useFormContext().control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectShift
              {...props}
              placeholder={placeholder}
              size={size}
              className={className}
              loading={isLoading}
              organizationId={organizationId}
              locationId={locationId}
              status={status}
              includeLocation={includeLocation}
              includeProvider={includeProvider}
              includePerson={includePerson}
              defaultOptions={defaultOptions}
              pageSize={pageSize}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
              onValueChange={setQuery}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for shift selector
 */
export interface SearchShiftProps extends SelectShiftProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name: string;
  /** Default value (becomes defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for shift selector
 */
export function SearchShift({
  group,
  name = "shift",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchShiftProps) {
  const { selection, onClear, onSelectionChange } = useSearchValue<Shift>({
    name,
    group,
    defaultValue,
    data: [], // Will be provided by the component itself
  });

  return (
    <SelectShift
      {...props}
      defaultQuery={defaultValue} // ✅ Map defaultValue to defaultQuery for search pattern
      useDialog={useDialog}
      selection={selection} // ✅ From search hook
      onSelect={onSelectionChange ?? onSelect} // ✅ From search hook
      onClear={onClear} // ✅ From search hook
    />
  );
}
