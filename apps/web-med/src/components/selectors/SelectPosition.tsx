"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { JobPositionStatus, JobPostType } from "@/api";
import type { Position } from "@/hooks/selectors/use-select-position";

import { useSelectPosition } from "@/hooks/selectors/use-select-position";

const i18n = {
  en: {
    label: "Position",
    description: "Select a position",
    placeholder: "Select a position",
  },
};

/**
 * Base component props for position selector
 */
export interface SelectPositionProps
  extends Omit<SelectorProps<Position>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter positions by organization ID */
  organizationId?: string;
  /** Filter positions by facility ID */
  facilityId?: string;
  /** Filter positions by department ID */
  departmentId?: string;
  /** Filter positions by location ID */
  locationId?: string;
  /** Filter positions by status */
  status?: JobPositionStatus[];
  /** Filter positions by type */
  type?: JobPostType[];
  /** Include provider data */
  includeProvider?: boolean;
  /** Include location data */
  includeLocation?: boolean;
  /** Include facility data */
  includeFacility?: boolean;
  /** Include department data */
  includeDepartment?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected position */
  defaultSelection?: Position;
  /** Default options to pre-populate */
  defaultOptions?: Position[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Position selector component
 */
export function SelectPosition({
  loading = false,
  enabled = true,
  useDialog = false,
  organizationId,
  facilityId,
  departmentId,
  locationId,
  status,
  type,
  includeProvider = true,
  includeLocation = true,
  includeFacility = true,
  includeDepartment = true,
  defaultValue,
  children,
  onSelect,
  defaultQuery = "",
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectPositionProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectPosition({
    enabled,
    organizationId,
    facilityId,
    departmentId,
    locationId,
    status,
    type,
    includeProvider,
    includeLocation,
    includeFacility,
    includeDepartment,
    defaultQuery: defaultValue ?? defaultQuery, // Map defaultValue to defaultQuery for compatibility
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  // Enhanced data is now handled by the hook
  const enhancedData = useMemo(() => {
    const positions = [...data];
    if (
      selection &&
      !positions.find((position) => position.id === selection.id)
    ) {
      positions.unshift(selection);
    }
    return positions;
  }, [data, selection]);

  const handleRenderValue = useCallback((position: Position) => {
    return position.role || position.summary || i18n.en.placeholder;
  }, []);

  const handleRenderItem = useCallback((position: Position) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{position.role}</div>
        {position.summary && (
          <div className="text-sm text-muted-foreground">
            {position.summary}
          </div>
        )}
        {position.location?.name && (
          <div className="text-xs text-muted-foreground">
            Location: {position.location.name}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Position>
      useDialog={useDialog}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      {...props}
      loading={isLoading}
      placeholder={placeholder}
      label={placeholder}
      data={enhancedData}
      open={open}
      onOpenChange={setOpen}
      value={query} // ✅ ONLY for query string
      selection={selection ?? undefined} // ✅ Current selection
      onValueChange={setQuery} // ✅ ONLY for query changes
      onSelect={setSelection} // ✅ For selection changes
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
      // ❌ NO defaultValue prop here - this was the critical bug
    >
      {children}
    </Selector>
  );
}

/**
 * Form field props for position selector
 */
export interface SelectPositionFieldProps
  extends Omit<SelectorProps<Position>, "data" | "onValueChange"> {
  /** Field name in form */
  name?: string;
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Field label (default: "Position") */
  label?: string;
  /** Field description (default: "Select a position") */
  description?: string;
  /** Placeholder text (default: "Select a position") */
  placeholder?: string;
  /** Filter positions by organization ID */
  organizationId?: string;
  /** Filter positions by facility ID */
  facilityId?: string;
  /** Filter positions by department ID */
  departmentId?: string;
  /** Filter positions by location ID */
  locationId?: string;
  /** Filter positions by status */
  status?: JobPositionStatus[];
  /** Filter positions by type */
  type?: JobPostType[];
  /** Include provider data */
  includeProvider?: boolean;
  /** Include location data */
  includeLocation?: boolean;
  /** Include facility data */
  includeFacility?: boolean;
  /** Include department data */
  includeDepartment?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Position[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for position selector
 */
export function SelectPositionField({
  organizationId,
  facilityId,
  departmentId,
  locationId,
  status,
  type,
  includeProvider = true,
  includeLocation = true,
  includeFacility = true,
  includeDepartment = true,
  defaultOptions,
  pageSize = 5,
  enabled = true,
  loading,
  name = "position",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  size = "lg",
  className,
  ...props
}: SelectPositionFieldProps) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectPosition({
    enabled,
    organizationId,
    facilityId,
    departmentId,
    locationId,
    status,
    type,
    includeProvider,
    includeLocation,
    includeFacility,
    includeDepartment,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <FormField
      control={useFormContext().control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectPosition
              {...props}
              placeholder={placeholder}
              size={size}
              className={className}
              loading={isLoading}
              organizationId={organizationId}
              facilityId={facilityId}
              departmentId={departmentId}
              locationId={locationId}
              status={status}
              type={type}
              includeProvider={includeProvider}
              includeLocation={includeLocation}
              includeFacility={includeFacility}
              includeDepartment={includeDepartment}
              defaultOptions={defaultOptions}
              pageSize={pageSize}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
              onValueChange={setQuery}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for position selector
 */
export interface SearchPositionProps extends SelectPositionProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name: string;
  /** Default value (becomes defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for position selector
 */
export function SearchPosition({
  group,
  name = "position",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchPositionProps) {
  const { selection, onClear, onSelectionChange } = useSearchValue<Position>({
    name,
    group,
    defaultValue,
    data: [], // Will be provided by the component itself
  });

  return (
    <SelectPosition
      {...props}
      defaultQuery={defaultValue} // ✅ Map defaultValue to defaultQuery for search pattern
      useDialog={useDialog}
      selection={selection} // ✅ From search hook
      onSelect={onSelectionChange ?? onSelect} // ✅ From search hook
      onClear={onClear} // ✅ From search hook
    />
  );
}
