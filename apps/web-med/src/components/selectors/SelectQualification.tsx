"use client";

import { use<PERSON>allback, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { QualificationStatus, QualificationType } from "@/api";
import type { Qualification } from "@/hooks/selectors/use-select-qualification";

import { useSelectQualification } from "@/hooks/selectors/use-select-qualification";

import QualificationStatusBadge from "../common/QualificationStatus";
import QualificationTypeBadge from "../common/QualificationType";

const i18n = {
  en: {
    label: "Qualification",
    description: "Select a qualification",
    placeholder: "Select a qualification",
    actions: {
      selectQualification: "Select Qualification",
    },
  },
};

/**
 * Base component props for qualification selector
 */
export interface SelectQualificationProps
  extends Omit<
    SelectorProps<Qualification>,
    "loading" | "data" | "onValueChange"
  > {
  loading?: boolean;
  enabled?: boolean;
  useDialog?: boolean;
  defaultValue?: string;
  providerId?: string;
  type?: QualificationType;
  status?: QualificationStatus;
  defaultQuery?: string;
  defaultSelection?: Qualification;
  defaultDebounce?: number;
  size?: "sm" | "md" | "lg";
}

export function SelectQualification({
  loading = false,
  enabled = true,
  useDialog = false,
  defaultValue,
  providerId,
  type,
  status,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  variant = "outline",
  children,
  onSelect,
  ...props
}: SelectQualificationProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectQualification({
    enabled,
    providerId,
    type,
    status,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const qualifications = [...(data ?? [])];
    if (
      selection &&
      !qualifications.find((qualification) => qualification.id === selection.id)
    ) {
      qualifications.unshift(selection);
    }
    return qualifications;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: Qualification) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("truncate text-base font-semibold", {
            "text-sm": size === "sm",
            "text-base": size === "md",
            "text-lg": size === "lg",
          })}
        >
          {item.name}
        </h3>
        <div className="flex items-center gap-2">
          <QualificationStatusBadge status={item.status} />
          <QualificationTypeBadge type={item.type} />
        </div>
      </div>
    ),
    [size],
  );

  const renderLoading = useCallback(
    () => (
      <div className="flex flex-col gap-1 text-start">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
    ),
    [],
  );

  return (
    <Selector<Qualification>
      size={size}
      className={cn(
        {
          "h-16 p-2": size === "lg",
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
        },
        className,
      )}
      loading={isLoading}
      useDialog={useDialog}
      data={enhancedData}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
      placeholder={placeholder}
      label={placeholder}
      variant={variant}
      renderItem={renderItem}
      renderLoading={renderLoading}
      {...props}
    >
      {children}
    </Selector>
  );
}

export interface SelectQualificationFieldProps
  extends SelectQualificationProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectQualificationField({
  name = "qualification",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectQualificationFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectQualification
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchQualificationProps extends SelectQualificationProps {
  group: string;
  name: string;
  defaultValue?: string;
}

export function SearchQualification({
  group,
  name = "qualification",
  defaultValue,
  onSelect,
  ...props
}: SearchQualificationProps) {
  const { selection, onClear, onSelectionChange } =
    useSearchValue<Qualification>({
      name,
      group,
      defaultValue,
      data: [],
    });

  return (
    <SelectQualification
      {...props}
      size="sm"
      onClear={onClear}
      selection={selection ?? undefined}
      onSelectionChange={useCallback(
        async (value?: Qualification | null) => {
          onSelectionChange(value);
          if (value) {
            await onSelect?.(value);
          }
        },
        [onSelectionChange, onSelect],
      )}
    />
  );
}
