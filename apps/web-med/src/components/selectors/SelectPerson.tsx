"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import Image from "next/image";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@axa/ui/selectors/Selector";
import ContactName from "@axa/ui/common/ContactName";
import { cn } from "@axa/ui/lib";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { PersonRole } from "@/api";
import type { Person } from "@/hooks/selectors/use-select-person";

import { useSelectPerson } from "@/hooks/selectors/use-select-person";

const i18n = {
  en: {
    addPerson: "Add Person",
    label: "Person",
    description: "The person to contact",
    placeholder: "Select a person to contact",
    searchPerson: "Search person...",
    avatarFor: "avatar image for ",
  },
};

// Re-export types for backward compatibility
export type PersonStructure = Person;

// Extend Person to match PersonPartial interface from web-tech
export interface PersonPartial extends GenericNode {
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string | null;
  avatar?: string | null;
  level?: number | null;
  userRole?: string | null;
  isUser?: boolean | null;
}

/**
 * Pre-built SelectPerson component props (based on web-tech implementation)
 */
export interface SelectPersonCoreProps<PersonType extends PersonPartial>
  extends SelectorProps<PersonType> {
  placeholder?: string;
}

/**
 * Pre-built SelectPerson component (adapted from web-tech)
 */
function SelectPersonCore<PersonType extends PersonPartial>({
  children,
  loading = false,
  placeholder = i18n.en.searchPerson,
  ...props
}: SelectPersonCoreProps<PersonType>) {
  return (
    <Selector<PersonType>
      loading={loading}
      label={i18n.en.addPerson}
      placeholder={placeholder}
      renderLoading={() => (
        <div className="flex w-full flex-col items-center justify-center gap-1">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      )}
      renderValue={useCallback(
        (person: PersonType) => `${person.firstName} ${person.lastName}`,
        [],
      )}
      renderItem={useCallback(
        (person: PersonType) => {
          const { name, initials } = {
            name: `${person.firstName ?? ""} ${person.lastName ?? ""}`.trim(),
            initials:
              [(person.firstName ?? " ")[0], (person.lastName ?? " ")[0]]
                .join("")
                .trim() || "AA",
          };
          return (
            <div className="flex items-center gap-2">
              {loading ? (
                <Skeleton className="size-9" />
              ) : (
                <Avatar className="size-9 rounded-lg">
                  <AvatarImage asChild src={person.avatar ?? ""}>
                    <Image
                      src={person.avatar ?? ""}
                      alt={i18n.en.avatarFor + name}
                      width={32}
                      height={32}
                    />
                  </AvatarImage>
                  <AvatarFallback className="rounded-lg">
                    {initials}
                  </AvatarFallback>
                </Avatar>
              )}

              <dl className="grid flex-1 gap-1">
                {loading ? (
                  <Skeleton className="size-9" />
                ) : (
                  <ContactName
                    showCopyButton={false}
                    name={name}
                    className="font-semibold text-foreground"
                  />
                )}
              </dl>
            </div>
          );
        },
        [loading],
      )}
      {...props}
    >
      {children}
    </Selector>
  );
}

/**
 * Base component props for person selector
 */
export interface SelectPersonProps
  extends Omit<SelectPersonCoreProps<Person>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Filter by person roles */
  roles?: PersonRole[];
  /** Default query string */
  defaultQuery?: string;
  /** Default selected person */
  defaultSelection?: Person;
  /** Default options to pre-populate */
  defaultOptions?: Person[];
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Search group for integration */
  group?: string;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
  /** Class name for the selector */
  className?: string;
  /** Placeholder text */
  placeholder?: string;
}

/**
 * Person selector component (following SelectOrganization pattern)
 */
export function SelectPerson({
  loading = false,
  enabled = true,
  useDialog = false,
  children,
  onSelect,
  organizationId,
  roles,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  group,
  size = "md",
  className,
  placeholder = i18n.en.searchPerson,
  ...props
}: SelectPersonProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectPerson({
    enabled,
    organizationId,
    roles,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const people = [...data];
    if (selection && !people.find((person) => person.id === selection.id)) {
      people.unshift(selection);
    }
    return people;
  }, [data, selection]);

  return (
    <SelectPersonCore<Person>
      useDialog={useDialog}
      className={className}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
    >
      {children}
    </SelectPersonCore>
  );
}

/**
 * Pre-built SelectPersonField component props (adapted from web-tech)
 */
export interface SelectPersonFieldCoreProps<DataType extends PersonPartial>
  extends SelectPersonCoreProps<DataType> {
  name?: "person.id";
  label?: string;
  description?: string;
  placeholder?: string;
  onQueryChange?: (value: string) => void;
}

/**
 * Pre-built SelectPersonField component (adapted from web-tech)
 */
function SelectPersonFieldCore<DataType extends PersonPartial>({
  loading = false,
  name = "person.id" as const,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  onQueryChange,
  ...props
}: SelectPersonFieldCoreProps<DataType>) {
  const form = useFormContext<{
    person?: DataType;
    "person.id"?: DataType["id"];
    personId?: DataType["id"];
  }>();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const selection = data?.find((person) => person.id === field.value);
        return (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <SelectPersonCore
                {...props}
                loading={loading}
                placeholder={placeholder}
                data={data ?? []}
                selection={selection}
                useDialog={false}
                onValueChange={(value) => {
                  onQueryChange?.(value);
                }}
                onSelect={async (person) => {
                  await onSelect?.(person);
                  field.onChange(person.id);
                  form.setValue("personId", person.id, {
                    shouldDirty: true,
                  });
                  form.setValue(
                    "person",
                    {
                      id: person.id,
                      firstName: person.firstName,
                      lastName: person.lastName,
                      email: person.email ?? "",
                      phone: person.phone ?? "",
                      avatar: person.avatar ?? "",
                      isUser: person.isUser ?? false,
                    } as DataType,
                    {
                      shouldDirty: true,
                    },
                  );
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

/**
 * Form field props for person selector
 */
export interface SelectPersonFieldProps<DataType extends Person = Person>
  extends Omit<SelectPersonFieldCoreProps<DataType>, "data" | "onValueChange"> {
  /** Override loading state */
  loading?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Filter by person roles */
  roles?: PersonRole[];
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Person[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Field label (default: "Person") */
  label?: string;
  /** Field description (default: "The person to contact") */
  description?: string;
  /** Placeholder text (default: "Select a person to contact") */
  placeholder?: string;
  /** Size of the selector (default: "md") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for person selector (following SelectOrganization pattern)
 */
export function SelectPersonField<DataType extends Person = Person>({
  organizationId,
  roles,
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  size = "md",
  className,
  onSelect,
  onQueryChange,
  ...props
}: SelectPersonFieldProps<DataType>) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectPerson({
    enabled,
    organizationId,
    roles,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectPersonFieldCore
      label={label}
      description={description}
      placeholder={placeholder}
      className={className}
      {...props}
      loading={isLoading}
      data={data as unknown as DataType[]}
      onValueChange={setQuery}
      onQueryChange={(value) => {
        setQuery(value);
        onQueryChange?.(value);
      }}
      onSelect={onSelect}
    />
  );
}

/**
 * Search component props for person selector
 */
export interface SearchPersonProps extends SelectPersonProps {
  /** Search group identifier */
  group: string;
  /** Field name (default: "person") */
  name?: string;
  /** Default value */
  defaultValue?: string;
}

/**
 * Search component for person selector (following SelectOrganization pattern)
 * FIXED: Now properly passes hook data instead of empty array
 */
export function SearchPerson({
  group,
  name = "person",
  defaultValue,
  onSelect,
  useDialog = false,
  enabled = true,
  organizationId,
  roles,
  defaultOptions = [],
  pageSize = 5,
  ...props
}: SearchPersonProps) {
  // Get actual data from the hook instead of passing empty array
  const { data, loading: hookLoading } = useSelectPerson({
    enabled,
    organizationId,
    roles,
    defaultOptions,
    pageSize,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Person>({
    name,
    group,
    defaultValue,
    data, // CRITICAL FIX: Pass actual data instead of empty array
  });

  const handleSelect = useCallback(
    async (person: Person) => {
      await onSelect?.(person);
      onSelectionChange(person);
    },
    [onSelect, onSelectionChange],
  );

  return (
    <SelectPerson
      {...props}
      useDialog={useDialog}
      selection={selection}
      onSelect={handleSelect}
      // Pass the actual data from hook
      enabled={enabled}
      organizationId={organizationId}
      roles={roles}
      defaultOptions={defaultOptions}
      pageSize={pageSize}
    />
  );
}
