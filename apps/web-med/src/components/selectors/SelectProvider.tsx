"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Provider } from "@/hooks/selectors/use-select-provider";

import { ProviderStatus } from "@/api";
import PreviewProvider from "@/components/shared/PreviewProvider";
import { useSelectProvider } from "@/hooks/selectors/use-select-provider";

const i18n = {
  en: {
    label: "Provider",
    description: "Medical professional",
    placeholder: "Select a provider",
  },
};

// Re-export types for backward compatibility
type PartialProvider = Provider;
export type ProviderStructure = Provider;

/**
 * Base component props for provider selector
 */
export interface SelectProviderProps
  extends Omit<SelectorProps<Provider>, "data"> {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter providers by organization ID */
  organizationId?: string;
  /** Filter providers by status */
  status?: ProviderStatus;
  /** Filter providers by specialty */
  specialties?: string[];
  /** Filter providers by location ID */
  locationId?: string;
  /** Filter providers by facility ID */
  facilityId?: string;
  /** Filter providers by minimum rating */
  minRating?: number;
  /** Filter providers by experience level (years) */
  minExperience?: number;
  /** Filter providers by availability status */
  availabilityStatus?: "AVAILABLE" | "BUSY" | "OFF_DUTY";
  /** Include person data */
  includePerson?: boolean;
  /** Include qualifications data */
  includeQualifications?: boolean;
  /** Include verification data */
  includeVerification?: boolean;
  /** Include reviews data */
  includeReviews?: boolean;
  /** Include experiences data */
  includeExperiences?: boolean;
  /** Include specialties data */
  includeSpecialties?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected provider */
  defaultSelection?: Provider;
  /** Default options to pre-populate */
  defaultOptions?: Provider[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Provider selector component
 */
export function SelectProvider({
  loading = false,
  enabled = true,
  useDialog = false,
  organizationId,
  status = ProviderStatus.ACTIVE,
  specialties,
  locationId,
  facilityId,
  minRating,
  minExperience,
  availabilityStatus,
  includePerson = true,
  includeQualifications = false,
  includeVerification = false,
  includeReviews = false,
  includeExperiences = false,
  includeSpecialties = false,
  defaultValue,
  children,
  onSelect,
  defaultQuery = "",
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectProviderProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectProvider({
    enabled,
    organizationId,
    status,
    specialties,
    locationId,
    facilityId,
    minRating,
    minExperience,
    availabilityStatus,
    includePerson,
    includeQualifications,
    includeVerification,
    includeReviews,
    includeExperiences,
    includeSpecialties,
    defaultQuery: defaultValue ?? defaultQuery, // Map defaultValue to defaultQuery for compatibility
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  // Enhanced data is now handled by the hook
  const enhancedData = useMemo(() => {
    const providers = [...data];
    if (
      selection &&
      !providers.find((provider) => provider.id === selection.id)
    ) {
      providers.unshift(selection);
    }
    return providers;
  }, [data, selection]);

  const handleRenderValue = useCallback((provider: Provider) => {
    if (provider.person) {
      return `${provider.person.firstName} ${provider.person.lastName}`;
    }
    return provider.title ?? i18n.en.placeholder;
  }, []);

  const handleRenderItem = useCallback(
    (provider: Provider) => {
      return (
        <PreviewProvider loading={isLoading} provider={provider} size={size} />
      );
    },
    [isLoading, size],
  );

  const renderLoading = useCallback(() => {
    return <PreviewProvider loading={isLoading} />;
  }, [isLoading]);

  return (
    <Selector<Provider>
      useDialog={useDialog}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      {...props}
      loading={isLoading}
      placeholder={placeholder}
      label={placeholder}
      data={enhancedData}
      open={open}
      onOpenChange={setOpen}
      value={query} // ✅ ONLY for query string
      selection={selection ?? undefined} // ✅ Current selection
      onValueChange={setQuery} // ✅ ONLY for query changes
      onSelect={setSelection} // ✅ For selection changes
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
      // ❌ NO defaultValue prop here - this was the critical bug
    >
      {children}
    </Selector>
  );
}

/**
 * Form field props for provider selector
 */
export interface SelectProviderFieldProps
  extends Omit<SelectorProps<Provider>, "data" | "onValueChange"> {
  /** Field name in form */
  name?: string;
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Field label (default: "Provider") */
  label?: string;
  /** Field description (default: "Medical professional") */
  description?: string;
  /** Placeholder text (default: "Select a provider") */
  placeholder?: string;
  /** Filter providers by organization ID */
  organizationId?: string;
  /** Filter providers by status */
  status?: ProviderStatus;
  /** Filter providers by specialty */
  specialties?: string[];
  /** Filter providers by location ID */
  locationId?: string;
  /** Filter providers by facility ID */
  facilityId?: string;
  /** Filter providers by minimum rating */
  minRating?: number;
  /** Filter providers by experience level (years) */
  minExperience?: number;
  /** Filter providers by availability status */
  availabilityStatus?: "AVAILABLE" | "BUSY" | "OFF_DUTY";
  /** Include person data */
  includePerson?: boolean;
  /** Include qualifications data */
  includeQualifications?: boolean;
  /** Include verification data */
  includeVerification?: boolean;
  /** Include reviews data */
  includeReviews?: boolean;
  /** Include experiences data */
  includeExperiences?: boolean;
  /** Include specialties data */
  includeSpecialties?: boolean;
  /** Default options to pre-populate */
  defaultOptions?: Provider[];
  /** Page size for pagination (default: 10) */
  pageSize?: number;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
  /** Size of the selector (default: "lg") */
  size?: "sm" | "md" | "lg";
}

/**
 * Form field component for provider selector
 */
export function SelectProviderField({
  organizationId,
  status = ProviderStatus.ACTIVE,
  specialties,
  locationId,
  facilityId,
  minRating,
  minExperience,
  availabilityStatus,
  includePerson = true,
  includeQualifications = false,
  includeVerification = false,
  includeReviews = false,
  includeExperiences = false,
  includeSpecialties = false,
  defaultOptions,
  pageSize = 5,
  enabled = true,
  loading,
  name = "provider",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  size = "lg",
  className,
  ...props
}: SelectProviderFieldProps) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectProvider({
    enabled,
    organizationId,
    status,
    specialties,
    locationId,
    facilityId,
    minRating,
    minExperience,
    availabilityStatus,
    includePerson,
    includeQualifications,
    includeVerification,
    includeReviews,
    includeExperiences,
    includeSpecialties,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <FormField
      control={useFormContext().control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectProvider
              {...props}
              placeholder={placeholder}
              size={size}
              className={className}
              loading={isLoading}
              organizationId={organizationId}
              status={status}
              specialties={specialties}
              locationId={locationId}
              facilityId={facilityId}
              minRating={minRating}
              minExperience={minExperience}
              availabilityStatus={availabilityStatus}
              includePerson={includePerson}
              includeQualifications={includeQualifications}
              includeVerification={includeVerification}
              includeReviews={includeReviews}
              includeExperiences={includeExperiences}
              includeSpecialties={includeSpecialties}
              defaultOptions={defaultOptions}
              pageSize={pageSize}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
              onValueChange={setQuery}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for provider selector
 */
export interface SearchProviderProps extends SelectProviderProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name: string;
  /** Default value (becomes defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for provider selector
 */
export function SearchProvider({
  group,
  name = "provider",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchProviderProps) {
  const { selection, onClear, onSelectionChange } = useSearchValue<Provider>({
    name,
    group,
    defaultValue,
    data: [], // Will be provided by the component itself
  });

  return (
    <SelectProvider
      {...props}
      defaultQuery={defaultValue} // ✅ Map defaultValue to defaultQuery for search pattern
      useDialog={useDialog}
      selection={selection} // ✅ From search hook
      onSelect={onSelectionChange ?? onSelect} // ✅ From search hook
      onClear={onClear} // ✅ From search hook
    />
  );
}
