"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { ApplicationStatus } from "@/api";
import type { Application } from "@/hooks/selectors/use-select-application";

import { useSelectApplication } from "@/hooks/selectors/use-select-application";

const i18n = {
  en: {
    label: "Application",
    description: "Select an application",
    placeholder: "Select an application",
  },
};

export interface SelectApplicationProps
  extends Omit<SelectorProps<Application>, "data"> {
  loading?: boolean;
  enabled?: boolean;
  jobId?: string;
  providerId?: string;
  organizationId?: string;
  status?: ApplicationStatus;
  includeProvider?: boolean;
  includeJob?: boolean;
  includeOrganization?: boolean;
  defaultQuery?: string;
  defaultSelection?: Application;
  defaultOptions?: Application[];
  pageSize?: number;
  defaultDebounce?: number;
  size?: "sm" | "md" | "lg";
}

export function SelectApplication({
  loading = false,
  enabled = true,
  useDialog = false,
  jobId,
  providerId,
  organizationId,
  status,
  includeProvider = true,
  includeJob = true,
  includeOrganization = false,
  defaultValue,
  children,
  onSelect,
  defaultQuery = "",
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectApplicationProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectApplication({
    enabled,
    jobId,
    providerId,
    organizationId,
    status,
    includeProvider,
    includeJob,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery, // Map defaultValue to defaultQuery for compatibility
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  // Enhanced data is now handled by the hook
  const enhancedData = useMemo(() => {
    const applications = [...data];
    if (
      selection &&
      !applications.find((application) => application.id === selection.id)
    ) {
      applications.unshift(selection);
    }
    return applications;
  }, [data, selection]);

  const handleRenderValue = useCallback((application: Application) => {
    const name = application.provider?.person
      ? `${application.provider.person.firstName} ${application.provider.person.lastName}`
      : "Unknown Provider";
    return name;
  }, []);

  const handleRenderItem = useCallback((application: Application) => {
    const name = application.provider?.person
      ? `${application.provider.person.firstName} ${application.provider.person.lastName}`
      : "Unknown Provider";
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{name}</div>
        <div className="text-sm text-muted-foreground">
          Status: {application.status}
        </div>
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Application>
      useDialog={useDialog}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      {...props}
      loading={isLoading}
      placeholder={placeholder}
      label={placeholder}
      data={enhancedData}
      open={open}
      onOpenChange={setOpen}
      value={query} // ✅ ONLY for query string
      selection={selection ?? undefined} // ✅ Current selection
      onValueChange={setQuery} // ✅ ONLY for query changes
      onSelect={setSelection} // ✅ For selection changes
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
      // ❌ NO defaultValue prop here - this was the critical bug
    >
      {children}
    </Selector>
  );
}

/**
 * Form field props for application selector
 */
export interface SelectApplicationFieldProps
  extends Omit<SelectorProps<Application>, "data" | "onValueChange"> {
  name?: string;
  loading?: boolean;
  label?: string;
  description?: string;
  placeholder?: string;
  jobId?: string;
  providerId?: string;
  organizationId?: string;
  status?: ApplicationStatus;
  includeProvider?: boolean;
  includeJob?: boolean;
  includeOrganization?: boolean;
  defaultOptions?: Application[];
  pageSize?: number;
  showLabel?: boolean;
  showDescription?: boolean;
  size?: "sm" | "md" | "lg";
}

export function SelectApplicationField({
  jobId,
  providerId,
  organizationId,
  status,
  includeProvider = true,
  includeJob = true,
  includeOrganization = false,
  defaultOptions,
  pageSize = 5,
  loading,
  name = "application",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  size = "lg",
  className,
  ...props
}: SelectApplicationFieldProps) {
  return (
    <FormField
      control={useFormContext().control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectApplication
              {...props}
              placeholder={placeholder}
              size={size}
              className={className}
              loading={loading}
              jobId={jobId}
              providerId={providerId}
              organizationId={organizationId}
              status={status}
              includeProvider={includeProvider}
              includeJob={includeJob}
              includeOrganization={includeOrganization}
              defaultOptions={defaultOptions}
              pageSize={pageSize}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchApplicationProps extends SelectApplicationProps {
  group: string;
  name: string;
  defaultValue?: string;
}

export function SearchApplication({
  group,
  name = "application",
  defaultValue,
  onSelect,
  useDialog = false,
  ...props
}: SearchApplicationProps) {
  const { selection, onClear, onSelectionChange } = useSearchValue<Application>(
    {
      name,
      group,
      defaultValue,
      data: [],
    },
  );

  return (
    <SelectApplication
      {...props}
      defaultQuery={defaultValue}
      useDialog={useDialog}
      selection={selection}
      onSelect={onSelectionChange ?? onSelect}
      onClear={onClear}
    />
  );
}
