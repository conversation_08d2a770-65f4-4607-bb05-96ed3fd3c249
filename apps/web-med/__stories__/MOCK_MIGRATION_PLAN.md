# Mock Data Migration Plan

## Current State Analysis

### Existing Mock Patterns

1. **Scattered Mock Data**: Mock data is spread across ~100+ story files
2. **Inconsistent Patterns**: Multiple approaches to mocking tRPC data
3. **Duplication**: Similar mock objects recreated in multiple files
4. **Maintenance Issues**: Changes require updates across many files

### Current Mock Locations

- `__stories__/mocks/organization.mocks.ts` - Basic organization mocks
- `__stories__/components/selectors/mocks.ts` - Comprehensive tRPC mocks (796 lines)
- `__stories__/components/shifts/data.tsx` - Detailed shift mock factory
- `__stories__/helpers.tsx` - Mock mutation utilities
- Individual story files with inline mocks

### Mock Data Types Identified

Based on analysis of 100+ story files, we use mocks for:

**Core Entities:**

- Organizations, Providers, People, Users
- Jobs, Applications, Offers, Contracts
- Shifts, Schedules, Time Blocks
- Facilities, Departments, Addresses
- Documents, Invoices, Payouts
- Incidents, Reviews, Specialties
- Qualifications, Medical Roles

**tRPC Endpoints:**

- Query mocks (getMany, get)
- Mutation mocks (create, update, delete)
- MSW handlers for API simulation

## Migration Strategy

### Phase 1: Centralized Mock Infrastructure

**Goal**: Create a unified mock data system in `__stories__/mocks/`

**Structure**:

```
__stories__/mocks/
├── index.ts                 # Main export file
├── factories/               # Data factories
│   ├── core.ts             # Base factory utilities
│   ├── organizations.ts    # Organization-related mocks
│   ├── providers.ts        # Provider-related mocks
│   ├── jobs.ts             # Job-related mocks
│   ├── shifts.ts           # Shift-related mocks
│   ├── facilities.ts       # Facility-related mocks
│   └── ...                 # Other entity factories
├── handlers/               # MSW/tRPC handlers
│   ├── index.ts            # Combined handlers export
│   ├── queries.ts          # Query handlers
│   └── mutations.ts        # Mutation handlers
├── presets/                # Pre-configured data sets
│   ├── scenarios.ts        # Common test scenarios
│   └── states.ts           # Different entity states
└── utils/                  # Mock utilities
    ├── helpers.ts          # Enhanced helper functions
    └── types.ts            # Mock-specific types
```

### Phase 2: Factory Pattern Implementation

**Goal**: Create consistent, flexible mock factories

**Features**:

- Type-safe mock generation
- Configurable overrides
- Relationship handling
- State-based presets
- Faker.js integration

### Phase 3: Story Migration

**Goal**: Systematically migrate existing stories

**Priority Order**:

1. **Tables** (highest usage) - 14 files
2. **Selectors** (most complex) - 17 files
3. **Forms** (moderate complexity) - 12 files
4. **Pages** (integration tests) - 50+ files
5. **Widgets** (specialized) - 8 files

### Phase 4: Enhanced Developer Experience

**Goal**: Make mock data easy to use and maintain

**Features**:

- Auto-completion for mock data
- Consistent API across all stories
- Easy scenario switching
- Performance optimization

## Implementation Plan

### Week 1: Infrastructure Setup

- [ ] Create centralized mock directory structure
- [ ] Implement base factory system
- [ ] Migrate existing organization.mocks.ts
- [ ] Create enhanced helpers

### Week 2: Core Factories

- [ ] Implement organization factories
- [ ] Implement provider factories
- [ ] Implement job/application factories
- [ ] Create tRPC handler system

### Week 3: Table Stories Migration

- [ ] Migrate ListOrganizations.stories.tsx
- [ ] Migrate ListProviders.stories.tsx
- [ ] Migrate ListJobs.stories.tsx
- [ ] Migrate ListApplications.stories.tsx
- [ ] Migrate remaining table stories

### Week 4: Selector Stories Migration

- [ ] Migrate SelectOrganization.stories.tsx
- [ ] Migrate SelectProvider.stories.tsx
- [ ] Migrate SelectFacility.stories.tsx
- [ ] Migrate remaining selector stories

### Week 5-6: Forms and Pages Migration

- [ ] Migrate form stories
- [ ] Migrate page stories
- [ ] Migrate widget stories

### Week 7: Cleanup and Optimization

- [ ] Remove old mock files
- [ ] Optimize performance
- [ ] Update documentation
- [ ] Add migration guide

## Success Metrics

### Before Migration

- ~100+ files with mock data
- Inconsistent mock patterns
- High maintenance overhead
- Duplication across files

### After Migration

- Single source of truth for mock data
- Consistent API across all stories
- Easy maintenance and updates
- Reusable mock factories
- Type-safe mock generation

## Risk Mitigation

### Potential Issues

1. **Breaking Changes**: Stories may break during migration
2. **Type Mismatches**: Mock data may not match API types
3. **Performance**: Centralized system may be slower
4. **Adoption**: Developers may resist new patterns

### Mitigation Strategies

1. **Incremental Migration**: Migrate in small batches
2. **Type Safety**: Use RouterOutputs types throughout
3. **Performance Testing**: Monitor story load times
4. **Documentation**: Provide clear migration examples

## Migration Examples

### Before (Current Pattern):

```typescript
// ListOrganizations.stories.tsx
import { trpcMsw } from "msw-trpc";

import type { AppRouter } from "@/api";

const t = trpcMsw.create<AppRouter>();

export default {
  parameters: {
    msw: {
      handlers: [
        t.organizations.getMany.query((req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.data({
              items: [
                {
                  id: "org-1",
                  name: "Test Hospital",
                  type: "HOSPITAL",
                  status: "ACTIVE",
                  // ... 50+ lines of mock data
                },
                // ... more inline mock objects
              ],
              total: 10,
            }),
          );
        }),
      ],
    },
  },
};
```

### After (New Pattern):

```typescript
// ListOrganizations.stories.tsx
import { mockData, mockHandlers } from "@/__stories__/mocks";

export default {
  parameters: {
    msw: {
      handlers: [mockHandlers.organizations.getMany({ count: 10 })],
    },
  },
};

// Or with custom data:
export const WithCustomData = {
  parameters: {
    msw: {
      handlers: [
        mockHandlers.organizations.getMany({
          count: 5,
          filters: { type: "HOSPITAL" },
        }),
      ],
    },
  },
};

// Or with presets:
export const HospitalSystem = {
  parameters: {
    msw: {
      handlers: [
        mockHandlers.organizations.get(
          mockData.presets.organization.activeHospital(),
        ),
      ],
    },
  },
};
```

## Implementation Status

### ✅ Completed Infrastructure:

- [x] Base factory system (`factories/core.ts`)
- [x] Type definitions (`utils/types.ts`)
- [x] Organization factory (`factories/organizations.ts`)
- [x] tRPC handlers system (`handlers/index.ts`)
- [x] Main API exports (`index.ts`)
- [x] Migration plan documentation

### 🚧 Next Steps (Ready to Execute):

#### Week 1: Complete Infrastructure

- [ ] Create provider factory (`factories/providers.ts`)
- [ ] Create job factory (`factories/jobs.ts`)
- [ ] Create application factory (`factories/applications.ts`)
- [ ] Update handlers with new factories

#### Week 2: Pilot Migration

- [ ] Migrate `ListOrganizations.stories.tsx`
- [ ] Migrate `SelectOrganization.stories.tsx`
- [ ] Test and refine API based on real usage
- [ ] Document migration patterns

#### Week 3-4: Systematic Migration

- [ ] Migrate all table stories (14 files)
- [ ] Migrate all selector stories (17 files)
- [ ] Migrate form stories (12 files)

#### Week 5-6: Complete Migration

- [ ] Migrate page stories (50+ files)
- [ ] Migrate widget stories (8 files)
- [ ] Remove old mock files
- [ ] Update documentation

## Ready to Start

The infrastructure is now complete and ready for use. The next step is to:

1. **Test the system** with a pilot migration of 2-3 stories
2. **Refine the API** based on real usage patterns
3. **Begin systematic migration** following the priority order
4. **Create additional factories** as needed for other entities

The centralized mock system provides:

- ✅ Type-safe mock generation
- ✅ Consistent API across all stories
- ✅ Easy maintenance and updates
- ✅ Reusable mock factories
- ✅ Flexible override system
- ✅ tRPC integration
- ✅ Migration compatibility
