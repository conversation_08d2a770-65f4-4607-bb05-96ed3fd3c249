import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { FacilityType } from "@/api";
import {
  SelectShift,
  SelectShiftField,
} from "@/components/selectors/SelectShift";

import { shifts } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectShift",
  component: SelectShift,
  parameters: {
    msw: {
      handlers: [shifts],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectShift>;

export default meta;
type Story = StoryObj<typeof SelectShift>;

export const Default: Story = {
  args: {
    placeholder: "Select a shift",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      shift: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectShiftField />
    </FormWrapper>
  ),
};

// ✅ Enhanced filtering capabilities - no more type assertions needed
export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a shift",
    organizationId: "org123",
  },
};

export const WithLocationFilter: Story = {
  args: {
    placeholder: "Select a shift",
    locationId: "location123",
  },
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select a shift",
    status: ["ACTIVE", "PENDING"],
  },
};

export const WithDefaultOptions: Story = {
  args: {
    placeholder: "Select a shift",
    defaultOptions: [
      {
        id: "shift1",
        summary: "Emergency Shift - 7AM-7PM",
        location: {
          name: "General Hospital",
          address: {
            formatted: "123 Main St",
            timeZone: "America/New_York",
            latitude: 40.7128,
            longitude: -74.006,
          },
          id: "location1",
          type: FacilityType.HOSPITAL,
        },
      },
      {
        id: "shift2",
        summary: "Night Shift - 7PM-7AM",
        location: {
          name: "City Medical Center",
          address: {
            formatted: "456 Elm St",
            timeZone: "America/New_York",
            latitude: 40.7128,
            longitude: -74.006,
          },
          id: "location2",
          type: FacilityType.HOSPITAL,
        },
      },
    ],
  },
};

export const WithCustomPageSize: Story = {
  args: {
    placeholder: "Select a shift",
    pageSize: 20,
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a shift",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a shift",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a shift",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a shift",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a shift",
    size: "md",
  },
};

export const IncludeMinimalData: Story = {
  args: {
    placeholder: "Select a shift",
    includeLocation: false,
    includeProvider: false,
    includePerson: false,
  },
};
