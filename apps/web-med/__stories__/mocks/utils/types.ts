/**
 * Type definitions for the centralized mock system
 */

import type { RouterOutputs } from "@/api";

// Base factory options
export interface MockFactoryOptions {
  /** Override specific fields */
  overrides?: Record<string, any>;
  /** Include related entities */
  include?: string[];
  /** Exclude specific fields */
  exclude?: string[];
  /** Use specific preset */
  preset?: string;
}

// Entity-specific mock types
export type MockOrganization =
  RouterOutputs["organizations"]["get"]["organization"];
export type MockOrganizations = RouterOutputs["organizations"]["getMany"];

export type MockProvider = RouterOutputs["providers"]["get"];
export type MockProviders = RouterOutputs["providers"]["getMany"];

export type MockJob = RouterOutputs["jobs"]["get"];
export type MockJobs = RouterOutputs["jobs"]["getMany"];

export type MockApplication = RouterOutputs["applications"]["get"];
export type MockApplications = RouterOutputs["applications"]["getMany"];

export type MockShift = RouterOutputs["shifts"]["get"];
export type MockShifts = RouterOutputs["shifts"]["getMany"];

export type MockContract = RouterOutputs["contracts"]["get"];
export type MockContracts = RouterOutputs["contracts"]["getMany"];

export type MockOffer = RouterOutputs["offers"]["get"];
export type MockOffers = RouterOutputs["offers"]["getMany"];

export type MockFacility = RouterOutputs["locations"]["get"];
export type MockFacilities = RouterOutputs["locations"]["getMany"];

export type MockDepartment = RouterOutputs["departments"]["get"];
export type MockDepartments = RouterOutputs["departments"]["getMany"];

export type MockPerson = RouterOutputs["people"]["get"];
export type MockPeople = RouterOutputs["people"]["getMany"];

export type MockUser = RouterOutputs["user"]["get"];
export type MockUsers = RouterOutputs["user"]["getMany"];

export type MockDocument = RouterOutputs["documents"]["get"];
export type MockDocuments = RouterOutputs["documents"]["getMany"];

export type MockInvoice = RouterOutputs["billing"]["invoices"]["get"];
export type MockInvoices = RouterOutputs["billing"]["invoices"]["getMany"];

export type MockPayout = RouterOutputs["billing"]["payouts"]["get"];
export type MockPayouts = RouterOutputs["billing"]["payouts"]["getMany"];

export type MockIncident = RouterOutputs["incidents"]["get"];
export type MockIncidents = RouterOutputs["incidents"]["getMany"];

export type MockSpecialty = RouterOutputs["specialties"]["get"];
export type MockSpecialties = RouterOutputs["specialties"]["getMany"];

export type MockQualification = RouterOutputs["qualifications"]["get"];
export type MockQualifications = RouterOutputs["qualifications"]["getMany"];

export type MockAddress = RouterOutputs["addresses"]["get"];
export type MockAddresses = RouterOutputs["addresses"]["getMany"];

export type MockContact = RouterOutputs["contacts"]["get"];
export type MockContacts = RouterOutputs["contacts"]["getMany"];

export type MockReview = RouterOutputs["reviews"]["get"];
export type MockReviews = RouterOutputs["reviews"]["getMany"];

// Pagination types
export interface MockPagination {
  pageIndex: number;
  pageSize: number;
}

export interface MockPaginatedResponse<T> {
  items: T[];
  total: number;
}

// Factory function types
export type MockFactory<T> = (
  overrides?: Partial<T>,
  options?: MockFactoryOptions,
) => T;
export type MockArrayFactory<T> = (
  count?: number,
  overrides?: Partial<T>,
  options?: MockFactoryOptions,
) => T[];
export type MockPaginatedFactory<T> = (
  count?: number,
  overrides?: Partial<T>,
  options?: MockFactoryOptions,
) => MockPaginatedResponse<T>;

// Handler types for MSW/tRPC
export interface MockQueryHandler {
  query: (input?: any) => any;
}

export interface MockMutationHandler {
  mutation: (input?: any) => any;
}

// Preset types
export interface MockPreset {
  name: string;
  description: string;
  data: Record<string, any>;
}

// Scenario types for complex test cases
export interface MockScenario {
  name: string;
  description: string;
  setup: () => Record<string, any>;
}

// State types for different entity states
export type EntityState =
  | "active"
  | "inactive"
  | "pending"
  | "approved"
  | "rejected"
  | "suspended"
  | "completed"
  | "cancelled";

// Relationship configuration
export interface RelationshipConfig {
  entity: string;
  count?: number;
  required?: boolean;
  factory?: string;
}

// Mock generation options
export interface GenerationOptions {
  seed?: number;
  locale?: string;
  relationships?: Record<string, RelationshipConfig>;
  constraints?: Record<string, any>;
}

// Story helper types
export interface StoryState<T> {
  loading: boolean;
  error: Error | null;
  data: T;
}

export interface TableStoryProps<T> {
  data: MockPaginatedResponse<T>;
  pagination: MockPagination;
  setPagination: (pagination: MockPagination) => void;
  loading?: boolean;
  error?: Error | null;
}

// Migration helper types
export interface MigrationHelper {
  from: string;
  to: string;
  converter: (oldData: any) => any;
}

// Validation types
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Performance tracking
export interface PerformanceMetrics {
  generationTime: number;
  memoryUsage: number;
  cacheHits: number;
  cacheMisses: number;
}

// Configuration types
export interface MockConfig {
  defaultCount: number;
  enableCaching: boolean;
  enablePerformanceTracking: boolean;
  defaultLocale: string;
  defaultSeed?: number;
}

// Export utility type helpers
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> &
  Partial<Pick<T, K>>;

// Mock data registry for tracking generated data
export interface MockRegistry {
  organizations: Map<string, MockOrganization>;
  providers: Map<string, MockProvider>;
  jobs: Map<string, MockJob>;
  applications: Map<string, MockApplication>;
  shifts: Map<string, MockShift>;
  // ... other entities
}

// Event types for mock system
export type MockEvent =
  | { type: "GENERATED"; entity: string; id: string; data: any }
  | { type: "CACHED"; entity: string; id: string }
  | { type: "ERROR"; entity: string; error: string };

export type MockEventListener = (event: MockEvent) => void;
