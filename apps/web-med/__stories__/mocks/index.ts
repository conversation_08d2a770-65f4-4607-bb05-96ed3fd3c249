/**
 * Centralized Mock Data System for Web-Med Storybook
 * 
 * This module provides a unified API for generating mock data across all stories.
 * It replaces scattered mock data with a consistent, type-safe, and maintainable system.
 * 
 * Usage Examples:
 * 
 * // Basic usage
 * import { mockData } from '@/__stories__/mocks';
 * const organization = mockData.organization();
 * const providers = mockData.providers(5);
 * 
 * // With overrides
 * const activeOrg = mockData.organization({ status: 'ACTIVE' });
 * 
 * // With relationships
 * const orgWithProviders = mockData.organization({
 *   providers: mockData.providers(3)
 * });
 * 
 * // For stories with tRPC
 * import { mockHandlers } from '@/__stories__/mocks';
 * export default {
 *   parameters: {
 *     msw: { handlers: mockHandlers.organizations.getMany() }
 *   }
 * };
 */

// Re-export all factories
export * from './factories';

// Re-export handlers
export * from './handlers';

// Re-export presets
export * from './presets';

// Re-export utilities
export * from './utils';

// Main mock data API
export { mockData } from './factories';

// tRPC handlers API
export { mockHandlers } from './handlers';

// Common scenarios
export { scenarios } from './presets';

// Helper utilities
export { createMockMutation, createMockQuery } from './utils';

/**
 * Quick access to commonly used mock data
 */
export const quick = {
  // Organizations
  organization: () => mockData.organization(),
  organizations: (count = 5) => mockData.organizations(count),
  
  // Providers
  provider: () => mockData.provider(),
  providers: (count = 5) => mockData.providers(count),
  
  // Jobs
  job: () => mockData.job(),
  jobs: (count = 5) => mockData.jobs(count),
  
  // Applications
  application: () => mockData.application(),
  applications: (count = 5) => mockData.applications(count),
  
  // Shifts
  shift: () => mockData.shift(),
  shifts: (count = 5) => mockData.shifts(count),
  
  // Common pagination
  pagination: () => ({ pageIndex: 0, pageSize: 10 }),
  
  // Empty states
  empty: {
    organizations: { items: [], total: 0 },
    providers: { items: [], total: 0 },
    jobs: { items: [], total: 0 },
    applications: { items: [], total: 0 },
    shifts: { items: [], total: 0 },
  },
};

/**
 * Story helpers for common patterns
 */
export const storyHelpers = {
  /**
   * Create loading state props
   */
  loading: <T>(emptyData: T) => ({
    loading: true,
    data: emptyData,
  }),
  
  /**
   * Create error state props
   */
  error: <T>(emptyData: T, message = 'Failed to load data') => ({
    loading: false,
    error: new Error(message),
    data: emptyData,
  }),
  
  /**
   * Create success state props
   */
  success: <T>(data: T) => ({
    loading: false,
    error: null,
    data,
  }),
  
  /**
   * Create table props with pagination
   */
  table: <T>(items: T[], total?: number) => ({
    data: { items, total: total ?? items.length },
    pagination: quick.pagination(),
    setPagination: () => {},
  }),
};

/**
 * Migration helpers for existing stories
 */
export const migration = {
  /**
   * Convert old inline mock to new factory
   */
  convertOrganization: (oldMock: any) => mockData.organization(oldMock),
  
  /**
   * Convert old array mocks to new factory
   */
  convertArray: <T>(oldMocks: any[], factory: (override?: any) => T): T[] =>
    oldMocks.map(mock => factory(mock)),
  
  /**
   * Convert old MSW handlers
   */
  convertHandlers: (oldHandlers: any[]) => {
    console.warn('Please migrate to new mockHandlers API');
    return oldHandlers;
  },
};

// Type exports for consumers
export type {
  MockOrganization,
  MockProvider,
  MockJob,
  MockApplication,
  MockShift,
  MockFactoryOptions,
} from './utils/types';

/**
 * Development utilities (only available in development)
 */
export const dev = process.env.NODE_ENV === 'development' ? {
  /**
   * Log mock data structure for debugging
   */
  inspect: (data: any) => {
    console.group('Mock Data Inspector');
    console.log('Type:', typeof data);
    console.log('Structure:', Object.keys(data));
    console.log('Data:', data);
    console.groupEnd();
    return data;
  },
  
  /**
   * Validate mock data against expected shape
   */
  validate: (data: any, expectedKeys: string[]) => {
    const actualKeys = Object.keys(data);
    const missing = expectedKeys.filter(key => !actualKeys.includes(key));
    const extra = actualKeys.filter(key => !expectedKeys.includes(key));
    
    if (missing.length > 0) {
      console.warn('Missing keys:', missing);
    }
    if (extra.length > 0) {
      console.warn('Extra keys:', extra);
    }
    
    return { missing, extra, valid: missing.length === 0 };
  },
} : {};

/**
 * Version information
 */
export const version = '1.0.0';
export const migrationStatus = {
  total: 100, // Approximate number of stories to migrate
  completed: 0,
  remaining: 100,
  percentage: 0,
};

/**
 * Default export for convenience
 */
export default {
  mockData,
  mockHandlers,
  scenarios,
  quick,
  storyHelpers,
  migration,
  version,
};
