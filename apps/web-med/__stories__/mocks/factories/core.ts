/**
 * Core factory utilities for generating mock data
 */

import { faker } from '@faker-js/faker';
import type { 
  MockFactoryOptions, 
  DeepPartial, 
  MockPaginatedResponse,
  GenerationOptions,
  MockConfig 
} from '../utils/types';

// Default configuration
const defaultConfig: MockConfig = {
  defaultCount: 5,
  enableCaching: true,
  enablePerformanceTracking: false,
  defaultLocale: 'en',
  defaultSeed: undefined,
};

// Global configuration
let config = { ...defaultConfig };

/**
 * Configure the mock system
 */
export function configureMocks(newConfig: Partial<MockConfig>) {
  config = { ...config, ...newConfig };
  if (config.defaultSeed) {
    faker.seed(config.defaultSeed);
  }
}

/**
 * Base factory class for creating mock data
 */
export class MockFactory<T> {
  private cache = new Map<string, T>();
  
  constructor(
    private generator: (overrides?: DeepPartial<T>) => T,
    private entityName: string
  ) {}

  /**
   * Generate a single entity
   */
  create(overrides?: DeepPartial<T>, options?: MockFactoryOptions): T {
    const cacheKey = this.getCacheKey(overrides, options);
    
    if (config.enableCaching && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const entity = this.generator(overrides);
    
    if (config.enableCaching) {
      this.cache.set(cacheKey, entity);
    }
    
    return entity;
  }

  /**
   * Generate multiple entities
   */
  createMany(
    count: number = config.defaultCount, 
    overrides?: DeepPartial<T>, 
    options?: MockFactoryOptions
  ): T[] {
    return Array.from({ length: count }, (_, index) => 
      this.create({ ...overrides, id: `${this.entityName}-${index + 1}` } as DeepPartial<T>, options)
    );
  }

  /**
   * Generate paginated response
   */
  createPaginated(
    count: number = config.defaultCount,
    overrides?: DeepPartial<T>,
    options?: MockFactoryOptions
  ): MockPaginatedResponse<T> {
    const items = this.createMany(count, overrides, options);
    return {
      items,
      total: count,
    };
  }

  /**
   * Clear cache for this factory
   */
  clearCache(): void {
    this.cache.clear();
  }

  private getCacheKey(overrides?: DeepPartial<T>, options?: MockFactoryOptions): string {
    return JSON.stringify({ overrides, options });
  }
}

/**
 * Create a new mock factory
 */
export function createFactory<T>(
  generator: (overrides?: DeepPartial<T>) => T,
  entityName: string
): MockFactory<T> {
  return new MockFactory(generator, entityName);
}

/**
 * Utility functions for common mock patterns
 */
export const mockUtils = {
  /**
   * Generate a random ID
   */
  id: (prefix = 'mock'): string => `${prefix}-${faker.string.uuid()}`,

  /**
   * Generate a random date within range
   */
  dateInRange: (start: Date, end: Date): Date => 
    faker.date.between({ from: start, to: end }),

  /**
   * Generate a random date in the past
   */
  pastDate: (days = 30): Date => 
    faker.date.past({ days }),

  /**
   * Generate a random date in the future
   */
  futureDate: (days = 30): Date => 
    faker.date.future({ days }),

  /**
   * Generate a random enum value
   */
  enumValue: <T extends Record<string, string>>(enumObj: T): T[keyof T] => {
    const values = Object.values(enumObj);
    return faker.helpers.arrayElement(values);
  },

  /**
   * Generate a random subset of an array
   */
  subset: <T>(array: T[], min = 1, max?: number): T[] => {
    const maxCount = max ?? array.length;
    const count = faker.number.int({ min, max: Math.min(maxCount, array.length) });
    return faker.helpers.arrayElements(array, count);
  },

  /**
   * Generate a random boolean with bias
   */
  boolean: (trueProbability = 0.5): boolean => 
    faker.datatype.boolean({ probability: trueProbability }),

  /**
   * Generate a random number within range
   */
  number: (min = 0, max = 100): number => 
    faker.number.int({ min, max }),

  /**
   * Generate a random decimal within range
   */
  decimal: (min = 0, max = 100, precision = 2): number => 
    faker.number.float({ min, max, fractionDigits: precision }),

  /**
   * Generate a random string with pattern
   */
  pattern: (pattern: string): string => 
    faker.helpers.fromRegExp(pattern),

  /**
   * Generate a random address
   */
  address: () => ({
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zipCode: faker.location.zipCode(),
    country: 'US',
  }),

  /**
   * Generate a random phone number
   */
  phone: (): string => faker.phone.number(),

  /**
   * Generate a random email
   */
  email: (): string => faker.internet.email(),

  /**
   * Generate a random name
   */
  name: () => ({
    first: faker.person.firstName(),
    last: faker.person.lastName(),
    full: faker.person.fullName(),
  }),

  /**
   * Generate a random company name
   */
  company: (): string => faker.company.name(),

  /**
   * Generate a random job title
   */
  jobTitle: (): string => faker.person.jobTitle(),

  /**
   * Generate a random medical specialty
   */
  medicalSpecialty: (): string => faker.helpers.arrayElement([
    'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'Emergency Medicine',
    'Internal Medicine', 'Surgery', 'Anesthesiology', 'Radiology', 'Pathology'
  ]),

  /**
   * Generate a random medical role
   */
  medicalRole: (): string => faker.helpers.arrayElement([
    'Registered Nurse', 'Licensed Practical Nurse', 'Nurse Practitioner',
    'Physician Assistant', 'Medical Doctor', 'Certified Nursing Assistant',
    'Medical Technician', 'Respiratory Therapist', 'Physical Therapist'
  ]),

  /**
   * Generate a random facility type
   */
  facilityType: (): string => faker.helpers.arrayElement([
    'Hospital', 'Clinic', 'Nursing Home', 'Rehabilitation Center',
    'Urgent Care', 'Surgery Center', 'Diagnostic Center'
  ]),

  /**
   * Generate a random status
   */
  status: (statuses: string[]): string => faker.helpers.arrayElement(statuses),

  /**
   * Generate a random priority
   */
  priority: (): string => faker.helpers.arrayElement(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),

  /**
   * Generate a random currency amount
   */
  currency: (min = 10, max = 1000): number => 
    faker.number.float({ min, max, fractionDigits: 2 }),

  /**
   * Generate a random percentage
   */
  percentage: (): number => 
    faker.number.float({ min: 0, max: 100, fractionDigits: 1 }),

  /**
   * Generate a random duration in minutes
   */
  duration: (min = 30, max = 480): number => 
    faker.number.int({ min, max }),

  /**
   * Generate a random shift time
   */
  shiftTime: () => {
    const start = faker.date.recent({ days: 7 });
    const duration = faker.number.int({ min: 4, max: 12 }) * 60 * 60 * 1000; // 4-12 hours
    const end = new Date(start.getTime() + duration);
    return { start, end };
  },

  /**
   * Generate a random description
   */
  description: (sentences = 3): string => 
    faker.lorem.sentences(sentences),

  /**
   * Generate a random note
   */
  note: (): string => faker.lorem.paragraph(),

  /**
   * Generate a random URL
   */
  url: (): string => faker.internet.url(),

  /**
   * Generate a random file name
   */
  fileName: (extension = 'pdf'): string => 
    `${faker.system.fileName()}.${extension}`,

  /**
   * Generate a random color
   */
  color: (): string => faker.internet.color(),

  /**
   * Generate a random avatar URL
   */
  avatar: (): string => faker.image.avatar(),
};

/**
 * Deep merge utility for combining objects
 */
export function deepMerge<T>(target: T, source: DeepPartial<T>): T {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key], source[key] as any);
      } else {
        result[key] = source[key] as any;
      }
    }
  }
  
  return result;
}

/**
 * Apply overrides to generated data
 */
export function applyOverrides<T>(base: T, overrides?: DeepPartial<T>): T {
  if (!overrides) return base;
  return deepMerge(base, overrides);
}

/**
 * Reset all factories and clear caches
 */
export function resetMocks(): void {
  // This will be implemented when we have factory instances
  console.log('Resetting all mock factories...');
}

/**
 * Get current configuration
 */
export function getConfig(): MockConfig {
  return { ...config };
}
