/**
 * Main factories export - Centralized mock data generation
 */

// Core utilities
export * from './core';

// Entity factories
export * from './organizations';
// TODO: Add other entity factories as they are created
// export * from './providers';
// export * from './jobs';
// export * from './applications';
// export * from './shifts';
// export * from './facilities';
// export * from './contracts';
// export * from './offers';
// export * from './people';
// export * from './users';

// Import factories
import { organizationFactory, organizationPresets } from './organizations';

/**
 * Main mock data API - provides easy access to all factories
 */
export const mockData = {
  // Organizations
  organization: organizationFactory.create.bind(organizationFactory),
  organizations: organizationFactory.createMany.bind(organizationFactory),
  organizationsPaginated: organizationFactory.createPaginated.bind(organizationFactory),
  
  // Organization presets
  presets: {
    organization: organizationPresets,
  },

  // TODO: Add other entities as factories are created
  // Providers
  // provider: providerFactory.create.bind(providerFactory),
  // providers: providerFactory.createMany.bind(providerFactory),
  // providersPaginated: providerFactory.createPaginated.bind(providerFactory),

  // Jobs
  // job: jobFactory.create.bind(jobFactory),
  // jobs: jobFactory.createMany.bind(jobFactory),
  // jobsPaginated: jobFactory.createPaginated.bind(jobFactory),

  // Applications
  // application: applicationFactory.create.bind(applicationFactory),
  // applications: applicationFactory.createMany.bind(applicationFactory),
  // applicationsPaginated: applicationFactory.createPaginated.bind(applicationFactory),

  // Shifts
  // shift: shiftFactory.create.bind(shiftFactory),
  // shifts: shiftFactory.createMany.bind(shiftFactory),
  // shiftsPaginated: shiftFactory.createPaginated.bind(shiftFactory),

  // Facilities
  // facility: facilityFactory.create.bind(facilityFactory),
  // facilities: facilityFactory.createMany.bind(facilityFactory),
  // facilitiesPaginated: facilityFactory.createPaginated.bind(facilityFactory),

  // Contracts
  // contract: contractFactory.create.bind(contractFactory),
  // contracts: contractFactory.createMany.bind(contractFactory),
  // contractsPaginated: contractFactory.createPaginated.bind(contractFactory),

  // Offers
  // offer: offerFactory.create.bind(offerFactory),
  // offers: offerFactory.createMany.bind(offerFactory),
  // offersPaginated: offerFactory.createPaginated.bind(offerFactory),

  // People
  // person: personFactory.create.bind(personFactory),
  // people: personFactory.createMany.bind(personFactory),
  // peoplePaginated: personFactory.createPaginated.bind(personFactory),

  // Users
  // user: userFactory.create.bind(userFactory),
  // users: userFactory.createMany.bind(userFactory),
  // usersPaginated: userFactory.createPaginated.bind(userFactory),

  // Documents
  // document: documentFactory.create.bind(documentFactory),
  // documents: documentFactory.createMany.bind(documentFactory),
  // documentsPaginated: documentFactory.createPaginated.bind(documentFactory),

  // Invoices
  // invoice: invoiceFactory.create.bind(invoiceFactory),
  // invoices: invoiceFactory.createMany.bind(invoiceFactory),
  // invoicesPaginated: invoiceFactory.createPaginated.bind(invoiceFactory),

  // Payouts
  // payout: payoutFactory.create.bind(payoutFactory),
  // payouts: payoutFactory.createMany.bind(payoutFactory),
  // payoutsPaginated: payoutFactory.createPaginated.bind(payoutFactory),

  // Incidents
  // incident: incidentFactory.create.bind(incidentFactory),
  // incidents: incidentFactory.createMany.bind(incidentFactory),
  // incidentsPaginated: incidentFactory.createPaginated.bind(incidentFactory),

  // Specialties
  // specialty: specialtyFactory.create.bind(specialtyFactory),
  // specialties: specialtyFactory.createMany.bind(specialtyFactory),
  // specialtiesPaginated: specialtyFactory.createPaginated.bind(specialtyFactory),

  // Qualifications
  // qualification: qualificationFactory.create.bind(qualificationFactory),
  // qualifications: qualificationFactory.createMany.bind(qualificationFactory),
  // qualificationsPaginated: qualificationFactory.createPaginated.bind(qualificationFactory),

  // Addresses
  // address: addressFactory.create.bind(addressFactory),
  // addresses: addressFactory.createMany.bind(addressFactory),
  // addressesPaginated: addressFactory.createPaginated.bind(addressFactory),

  // Contacts
  // contact: contactFactory.create.bind(contactFactory),
  // contacts: contactFactory.createMany.bind(contactFactory),
  // contactsPaginated: contactFactory.createPaginated.bind(contactFactory),

  // Reviews
  // review: reviewFactory.create.bind(reviewFactory),
  // reviews: reviewFactory.createMany.bind(reviewFactory),
  // reviewsPaginated: reviewFactory.createPaginated.bind(reviewFactory),

  /**
   * Utility methods
   */
  utils: {
    /**
     * Clear all factory caches
     */
    clearCaches: () => {
      organizationFactory.clearCache();
      // TODO: Add other factory cache clears as they are created
    },

    /**
     * Generate a complete organization with related data
     */
    completeOrganization: (overrides?: any) => {
      const organization = mockData.organization(overrides);
      return {
        organization,
        // TODO: Add related data generation
        // facilities: mockData.facilities(3, { organizationId: organization.id }),
        // members: mockData.people(5, { organizationId: organization.id }),
        // jobs: mockData.jobs(10, { organizationId: organization.id }),
      };
    },

    /**
     * Generate test data for a complete scenario
     */
    scenario: (name: string, config?: any) => {
      switch (name) {
        case 'healthcare-system':
          return {
            organizations: mockData.organizations(1, { type: 'HOSPITAL', class: 'HIRING' }),
            // TODO: Add related entities
          };
        case 'staffing-agency':
          return {
            organizations: mockData.organizations(1, { type: 'STAFFING_AGENCY', class: 'STAFFING' }),
            // TODO: Add related entities
          };
        default:
          throw new Error(`Unknown scenario: ${name}`);
      }
    },

    /**
     * Generate empty state data
     */
    empty: {
      organizations: { items: [], total: 0 },
      // TODO: Add other empty states
    },

    /**
     * Generate loading state data
     */
    loading: {
      organizations: { items: [], total: 0, loading: true },
      // TODO: Add other loading states
    },

    /**
     * Generate error state data
     */
    error: (message = 'Failed to load data') => ({
      organizations: { items: [], total: 0, error: new Error(message) },
      // TODO: Add other error states
    }),
  },
};

/**
 * Legacy compatibility - for migrating existing stories
 */
export const legacyMocks = {
  /**
   * Convert old organization mock format
   */
  organization: (oldMock: any) => {
    console.warn('Using legacy mock format. Please migrate to new mockData.organization()');
    return mockData.organization(oldMock);
  },

  /**
   * Convert old array format
   */
  organizations: (oldMocks: any[]) => {
    console.warn('Using legacy mock format. Please migrate to new mockData.organizations()');
    return oldMocks.map(mock => mockData.organization(mock));
  },
};

/**
 * Development helpers
 */
export const devHelpers = process.env.NODE_ENV === 'development' ? {
  /**
   * Log all available factories
   */
  listFactories: () => {
    console.group('Available Mock Factories');
    console.log('Organizations:', 'mockData.organization(), mockData.organizations()');
    // TODO: Add other factories as they are created
    console.groupEnd();
  },

  /**
   * Generate sample data for testing
   */
  generateSamples: () => ({
    organization: mockData.organization(),
    organizations: mockData.organizations(3),
    // TODO: Add other samples
  }),

  /**
   * Validate factory output
   */
  validate: (entityType: string, data: any) => {
    // TODO: Add validation logic
    console.log(`Validating ${entityType}:`, data);
    return true;
  },
} : {};

// Default export for convenience
export default mockData;
