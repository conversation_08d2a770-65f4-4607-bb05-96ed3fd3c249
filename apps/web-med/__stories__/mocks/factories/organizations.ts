/**
 * Organization mock factories
 */

import { createFactory, mockUtils, applyOverrides } from './core';
import type { MockOrganization, DeepPartial } from '../utils/types';

/**
 * Generate a mock organization
 */
function generateOrganization(overrides?: DeepPartial<MockOrganization>): MockOrganization {
  const name = mockUtils.company();
  const base: MockOrganization = {
    id: mockUtils.id('org'),
    name,
    slug: name.toLowerCase().replace(/[^a-z0-9]/g, '-'),
    type: mockUtils.enumValue({
      HOSPITAL: 'HOSPITAL',
      CLINIC: 'CLINIC',
      NURSING_HOME: 'NURSING_HOME',
      HOME_HEALTH: 'HOME_HEALTH',
      STAFFING_AGENCY: 'STAFFING_AGENCY',
      OTHER: 'OTHER',
    }),
    class: mockUtils.enumValue({
      HIRING: 'HIRING',
      STAFFING: 'STAFFING',
      BOTH: 'BOTH',
    }),
    status: mockUtils.enumValue({
      ACTIVE: 'ACTIVE',
      INACTIVE: 'INACTIVE',
      PENDING: 'PENDING',
      SUSPENDED: 'SUSPENDED',
    }),
    mode: mockUtils.enumValue({
      LIVE: 'LIVE',
      SANDBOX: 'SANDBOX',
      DEMO: 'DEMO',
    }),
    billingType: mockUtils.enumValue({
      DIRECT: 'DIRECT',
      AGENCY: 'AGENCY',
      HYBRID: 'HYBRID',
    }),
    description: mockUtils.description(2),
    website: mockUtils.url(),
    phone: mockUtils.phone(),
    email: mockUtils.email(),
    taxId: mockUtils.pattern('[0-9]{2}-[0-9]{7}'),
    licenseNumber: mockUtils.pattern('[A-Z]{2}[0-9]{6}'),
    
    // Address
    address: {
      id: mockUtils.id('addr'),
      street: mockUtils.address().street,
      city: mockUtils.address().city,
      state: mockUtils.address().state,
      zipCode: mockUtils.address().zipCode,
      country: 'US',
      latitude: mockUtils.decimal(-90, 90, 6),
      longitude: mockUtils.decimal(-180, 180, 6),
      createdAt: mockUtils.pastDate(365),
      updatedAt: mockUtils.pastDate(30),
    },

    // Billing information
    billing: {
      id: mockUtils.id('billing'),
      organizationId: '', // Will be set to organization id
      type: mockUtils.enumValue({
        DIRECT: 'DIRECT',
        AGENCY: 'AGENCY',
        HYBRID: 'HYBRID',
      }),
      paymentTerms: mockUtils.number(15, 60),
      creditLimit: mockUtils.currency(10000, 100000),
      currentBalance: mockUtils.currency(0, 5000),
      stripeCustomerId: `cus_${mockUtils.pattern('[A-Za-z0-9]{14}')}`,
      defaultPaymentMethodId: `pm_${mockUtils.pattern('[A-Za-z0-9]{24}')}`,
      autoPayEnabled: mockUtils.boolean(0.7),
      createdAt: mockUtils.pastDate(365),
      updatedAt: mockUtils.pastDate(30),
    },

    // Settings
    settings: {
      id: mockUtils.id('settings'),
      organizationId: '', // Will be set to organization id
      allowDirectBooking: mockUtils.boolean(0.8),
      requireBackgroundCheck: mockUtils.boolean(0.9),
      requireDrugTest: mockUtils.boolean(0.7),
      autoApproveApplications: mockUtils.boolean(0.3),
      notificationPreferences: {
        email: mockUtils.boolean(0.9),
        sms: mockUtils.boolean(0.6),
        push: mockUtils.boolean(0.8),
      },
      workflowSettings: {
        requireManagerApproval: mockUtils.boolean(0.6),
        autoAssignShifts: mockUtils.boolean(0.4),
        allowShiftSwapping: mockUtils.boolean(0.7),
      },
      createdAt: mockUtils.pastDate(365),
      updatedAt: mockUtils.pastDate(30),
    },

    // Metadata
    metadata: {
      bedCount: mockUtils.number(50, 500),
      employeeCount: mockUtils.number(100, 2000),
      specialties: mockUtils.subset([
        'Emergency Medicine',
        'Internal Medicine',
        'Surgery',
        'Pediatrics',
        'Cardiology',
        'Neurology',
        'Orthopedics',
        'Anesthesiology',
      ], 2, 5),
      certifications: mockUtils.subset([
        'Joint Commission',
        'CMS',
        'AAAHC',
        'NCQA',
        'DNV',
      ], 1, 3),
      technologies: mockUtils.subset([
        'Epic',
        'Cerner',
        'Meditech',
        'Allscripts',
        'NextGen',
      ], 1, 2),
    },

    // Timestamps
    createdAt: mockUtils.pastDate(365),
    updatedAt: mockUtils.pastDate(30),
    lastLoginAt: mockUtils.pastDate(7),

    // Relationships (will be populated by other factories if needed)
    facilities: [],
    members: [],
    jobs: [],
    contracts: [],
    applications: [],
    shifts: [],
    invoices: [],
    payouts: [],
    documents: [],
  };

  // Set foreign keys
  base.billing.organizationId = base.id;
  base.settings.organizationId = base.id;

  return applyOverrides(base, overrides);
}

/**
 * Organization factory instance
 */
export const organizationFactory = createFactory(generateOrganization, 'organization');

/**
 * Preset organizations for common scenarios
 */
export const organizationPresets = {
  /**
   * Active hospital organization
   */
  activeHospital: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: 'HOSPITAL',
      class: 'HIRING',
      status: 'ACTIVE',
      mode: 'LIVE',
      metadata: {
        bedCount: mockUtils.number(200, 800),
        employeeCount: mockUtils.number(500, 3000),
        specialties: [
          'Emergency Medicine',
          'Internal Medicine',
          'Surgery',
          'Cardiology',
          'Neurology',
        ],
      },
      ...overrides,
    }),

  /**
   * Staffing agency organization
   */
  staffingAgency: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: 'STAFFING_AGENCY',
      class: 'STAFFING',
      status: 'ACTIVE',
      mode: 'LIVE',
      billingType: 'AGENCY',
      metadata: {
        employeeCount: mockUtils.number(50, 500),
        specialties: mockUtils.subset([
          'Emergency Medicine',
          'Internal Medicine',
          'Surgery',
          'Pediatrics',
          'Cardiology',
        ], 3, 5),
      },
      ...overrides,
    }),

  /**
   * Small clinic organization
   */
  smallClinic: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: 'CLINIC',
      class: 'HIRING',
      status: 'ACTIVE',
      mode: 'LIVE',
      metadata: {
        bedCount: mockUtils.number(10, 50),
        employeeCount: mockUtils.number(20, 100),
        specialties: mockUtils.subset([
          'Internal Medicine',
          'Pediatrics',
          'Family Medicine',
        ], 1, 3),
      },
      ...overrides,
    }),

  /**
   * Pending organization (new signup)
   */
  pending: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      status: 'PENDING',
      mode: 'SANDBOX',
      billing: {
        currentBalance: 0,
        autoPayEnabled: false,
      },
      settings: {
        allowDirectBooking: false,
        autoApproveApplications: false,
      },
      ...overrides,
    }),

  /**
   * Demo organization for testing
   */
  demo: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      name: 'Demo Medical Center',
      slug: 'demo-medical-center',
      status: 'ACTIVE',
      mode: 'DEMO',
      type: 'HOSPITAL',
      class: 'HIRING',
      ...overrides,
    }),
};

/**
 * Generate multiple organizations with different types
 */
export function generateMixedOrganizations(count = 10): MockOrganization[] {
  const types = ['HOSPITAL', 'CLINIC', 'NURSING_HOME', 'STAFFING_AGENCY'] as const;
  const classes = ['HIRING', 'STAFFING', 'BOTH'] as const;
  const statuses = ['ACTIVE', 'INACTIVE', 'PENDING'] as const;

  return Array.from({ length: count }, (_, index) => {
    const type = types[index % types.length];
    const orgClass = classes[index % classes.length];
    const status = statuses[index % statuses.length];

    return organizationFactory.create({
      type,
      class: orgClass,
      status,
    });
  });
}

/**
 * Generate organizations for specific scenarios
 */
export const organizationScenarios = {
  /**
   * Large healthcare system with multiple facilities
   */
  healthcareSystem: () => ({
    organization: organizationPresets.activeHospital({
      name: 'Regional Healthcare System',
      metadata: {
        bedCount: 1200,
        employeeCount: 5000,
        specialties: [
          'Emergency Medicine',
          'Internal Medicine',
          'Surgery',
          'Cardiology',
          'Neurology',
          'Orthopedics',
          'Pediatrics',
          'Anesthesiology',
        ],
      },
    }),
    facilities: mockUtils.number(5, 15),
    members: mockUtils.number(20, 50),
    activeJobs: mockUtils.number(10, 30),
  }),

  /**
   * Startup staffing agency
   */
  startupAgency: () => ({
    organization: organizationPresets.staffingAgency({
      name: 'MedStaff Solutions',
      status: 'ACTIVE',
      metadata: {
        employeeCount: mockUtils.number(10, 50),
      },
    }),
    facilities: 0,
    members: mockUtils.number(3, 10),
    activeJobs: mockUtils.number(5, 15),
  }),

  /**
   * Rural clinic network
   */
  ruralNetwork: () => ({
    organization: organizationPresets.smallClinic({
      name: 'Rural Health Network',
      metadata: {
        bedCount: mockUtils.number(20, 80),
        employeeCount: mockUtils.number(50, 200),
      },
    }),
    facilities: mockUtils.number(3, 8),
    members: mockUtils.number(5, 15),
    activeJobs: mockUtils.number(2, 10),
  }),
};
