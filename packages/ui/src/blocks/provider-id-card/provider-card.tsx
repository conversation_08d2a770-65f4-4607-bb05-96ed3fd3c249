import { Dialog, DialogContent, DialogTrigger } from "@/ui/primitives/dialog";
import { MagicCard } from "@/ui/primitives/magic-card";

import { ProviderId } from "./provider-id";

export function ProviderIdCard() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {/* TODO: Add a lined background to the card */}
        <MagicCard className="cursor-pointer flex-col items-center justify-center whitespace-nowrap text-4xl shadow-2xl">
          <div>Provider ID</div>
        </MagicCard>
      </DialogTrigger>
      <DialogContent>
        <ProviderId />
      </DialogContent>
    </Dialog>
  );
}
