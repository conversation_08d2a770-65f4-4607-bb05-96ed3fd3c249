"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "recharts";

import { Card, CardContent } from "@/ui/primitives/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/ui/primitives/chart";

export interface StatusData {
  key: string;
  label: string;
  current: number;
  total: number;
}

export interface StatusProgressProps {
  statuses: StatusData[];
  unitLabel?: string;
  className?: string;
}

export function StatusProgress({
  statuses,
  unitLabel = "items",
  className,
}: StatusProgressProps) {
  // Generate chart config dynamically based on statuses
  const chartConfig = statuses.reduce(
    (config, status, index) => ({
      ...config,
      [status.key]: {
        label: status.label,
        color: `hsl(var(--chart-${index + 1}))`,
      },
    }),
    {},
  );

  // Prepare data for the radial chart
  const chartData = statuses.map((status, index) => ({
    activity: status.key,
    value: status.total > 0 ? (status.current / status.total) * 100 : 0,
    fill: `var(--color-${status.key})`,
  }));

  return (
    <Card className={className}>
      <CardContent className="grid size-full grid-cols-1 gap-3 p-3 sm:gap-4 sm:p-4 md:grid-cols-[1fr_1fr] lg:grid-cols-[0.75fr_1fr]">
        <div className="grid h-full items-center gap-2 sm:gap-3">
          {statuses.map((status, index) => (
            <div key={status.key} className="grid flex-1 auto-rows-min gap-0.5">
              <div className="flex items-center gap-1 text-xs text-muted-foreground sm:text-sm md:text-base">
                <span
                  className={`bg-chart-${index + 1} size-2.5 rounded-sm sm:size-3${index + 1} shrink-0`}
                />
                <span className="truncate">{status.label}</span>
              </div>
              <div className="flex items-baseline gap-1 text-sm font-bold tabular-nums leading-none sm:text-lg md:text-xl lg:text-2xl xl:text-3xl">
                <span className="truncate">
                  {status.current}/{status.total}
                </span>
                <span className="shrink-0 text-xs font-normal text-muted-foreground sm:text-sm md:text-base">
                  {unitLabel}
                </span>
              </div>
            </div>
          ))}
        </div>
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square size-full max-w-[95%] sm:max-w-[90%] md:max-w-[85%] lg:max-w-[80%]"
        >
          <RadialBarChart
            margin={{
              left: -10,
              right: -10,
              top: -10,
              bottom: -10,
            }}
            data={chartData}
            innerRadius="20%"
            barSize={24}
            startAngle={90}
            endAngle={450}
          >
            <PolarAngleAxis
              type="number"
              domain={[0, 100]}
              dataKey="value"
              tick={false}
            />
            <RadialBar dataKey="value" background cornerRadius={5} />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  indicator="line"
                  nameKey="value"
                  labelKey="activity"
                  labelFormatter={(_, value) => {
                    // @ts-expect-error bad typing from recharts
                    const [{ payload }] = value;
                    const status = statuses.find(
                      (s) => s.key === payload.activity,
                    );
                    return status?.label || payload.activity;
                  }}
                />
              }
              cursor={false}
            />
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
