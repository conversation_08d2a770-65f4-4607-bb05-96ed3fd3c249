"use client";

import { format, parseISO } from "date-fns";
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from "recharts";

import { cn } from "@/ui/lib";
import { Card, CardContent, CardHeader } from "@/ui/primitives/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/ui/primitives/chart";

export interface VolumeEntry {
  date: string;
  volume: number;
}

export interface VolumeTimelineProps {
  data?: VolumeEntry[];
  todayLabel?: string;
  periodLabel?: string;
  unitLabel?: string;
  chartLabel?: string;
  className?: string;
  timelineLabels?: {
    start: string;
    middle: string;
    end: string;
  };
}

// Utility function to format numbers with commas
// TODO: Move to shared utilities when location is determined
function formatNumber(num: number): string {
  if (num >= 100) {
    return num.toLocaleString();
  }
  return num.toString();
}

// Reusable metric card component
interface MetricSnapshotProps {
  label: string;
  value: number;
  unitLabel: string;
}

function MetricSnapshot({ label, value, unitLabel }: MetricSnapshotProps) {
  return (
    <div className="flex h-full flex-col items-start justify-center">
      <p className="text-nowrap text-xs font-light text-muted-foreground sm:text-sm">
        {label}
      </p>
      <p className="flex w-fit flex-col items-start gap-0">
        <span className="text-2xl font-semibold tabular-nums leading-none tracking-tight sm:text-3xl md:text-4xl">
          {formatNumber(value)}
        </span>
        <span className="text-xs font-normal tracking-normal text-muted-foreground">
          {unitLabel}
        </span>
      </p>
    </div>
  );
}

export function VolumeTimeline({
  data = [],
  todayLabel = "Volume today",
  periodLabel = "This period",
  unitLabel = "total",
  chartLabel = "Volume",
  className,
  timelineLabels = {
    start: "last week",
    middle: "today",
    end: "next week",
  },
}: VolumeTimelineProps) {
  const today = format(new Date(), "yyyy-MM-dd");

  const todayCount = data.find((entry) => entry.date === today)?.volume ?? 0;
  const totalCount = data.reduce((acc, entry) => acc + entry.volume, 0);

  return (
    <Card className={cn("flex w-full flex-col", className)}>
      <CardHeader className="flex flex-row items-stretch justify-stretch gap-4 pb-2 [&>div]:flex-1">
        <MetricSnapshot
          label={todayLabel}
          value={todayCount}
          unitLabel={unitLabel}
        />
        <MetricSnapshot
          label={periodLabel}
          value={totalCount}
          unitLabel={unitLabel}
        />
      </CardHeader>
      <CardContent className="flex flex-1 flex-col items-center gap-0">
        <ChartContainer
          config={{
            volume: {
              label: chartLabel,
              color: "hsl(var(--chart-1))",
            },
          }}
          className="w-full"
        >
          <LineChart
            accessibilityLayer
            margin={{
              left: 6,
              right: 6,
              top: 10,
            }}
            data={data}
          >
            <CartesianGrid
              strokeDasharray="4 4"
              vertical={false}
              stroke="hsl(var(--muted-foreground))"
              strokeOpacity={0.5}
            />
            <YAxis hide domain={["dataMin - 10", "dataMax + 10"]} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              interval="preserveStartEnd"
              tickFormatter={(value) => {
                return format(parseISO(value), "iii");
              }}
            />
            <Line
              dataKey="volume"
              type="natural"
              fill="var(--color-volume)"
              stroke="var(--color-volume)"
              strokeWidth={2}
              dot={false}
              activeDot={{
                fill: "var(--color-volume)",
                stroke: "var(--color-volume)",
                r: 4,
              }}
            />
            <ChartTooltip
              separator=" • "
              content={
                <ChartTooltipContent
                  className="gap-2"
                  indicator="line"
                  labelFormatter={(value) => {
                    return format(parseISO(value), "PPP");
                  }}
                />
              }
              cursor={false}
            />
          </LineChart>
        </ChartContainer>

        <div className="flex w-full flex-row items-center justify-between text-xs text-muted-foreground">
          <p>{timelineLabels.start}</p>
          <p>{timelineLabels.middle}</p>
          <p>{timelineLabels.end}</p>
        </div>
      </CardContent>
    </Card>
  );
}
