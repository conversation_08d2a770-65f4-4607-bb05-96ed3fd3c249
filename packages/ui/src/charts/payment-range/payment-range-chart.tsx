"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON>, <PERSON>hart, CartesianGrid, XAxis, YAxis } from "recharts";

import { cn } from "../../lib";
import { ChartContainer, ChartTooltip } from "../../primitives/chart";
import { Skeleton } from "../../primitives/skeleton";

export interface PaymentRange {
  /** Unique identifier for this range */
  id: string;
  /** Display label for this range (e.g., "Junior", "Senior", "Expert") */
  label: string;
  /** Minimum value for this range */
  min: number;
  /** Maximum value for this range */
  max: number;
  /**
   * Current rate(s) for this range
   * Single number = one bar
   * Array of two numbers = two bars side by side
   */
  rates: number | [number, number];
  /** Optional: how much of the range to show (0-1, defaults to 1 for full range) */
  fillRatio?: number;
}

export interface PaymentRangeChartProps {
  /** Array of payment ranges to display */
  ranges: PaymentRange[];
  /** Chart width in pixels */
  width?: number;
  /** Chart height in pixels */
  height?: number;
  /** Currency symbol (e.g., '$', '€', '£') */
  currency?: string;
  /** Rate unit (e.g., 'per hour', 'per day') */
  unit?: string;
  /** Whether to show rate labels */
  showLabels?: boolean;
  /** Whether to show X-axis */
  showXAxis?: boolean;
  /** Whether to show Y-axis */
  showYAxis?: boolean;
  /** Whether to show loading state */
  loading?: boolean;
  /** Custom class name for styling */
  className?: string;
  /** Color scheme for the chart */
  colors?: {
    primary: string;
    secondary: string;
    background: string;
  };
  /** Label for primary rate in tooltip (defaults to "Primary") */
  primaryRateLabel?: string;
  /** Label for secondary rate in tooltip (defaults to "Secondary") */
  secondaryRateLabel?: string;
}

// Type definitions for chart data
interface ChartDataItem {
  name: string;
  "rate-primary": number;
  "rate-secondary": number | null;
  min: number;
  max: number;
  isDual: boolean;
}
// Static validation function
const isValidNumber = (value: unknown): value is number =>
  typeof value === "number" && !isNaN(value) && isFinite(value);

// Static chart configuration
const CHART_CONFIG = {
  "rate-primary": {
    label: "Primary Rate",
    color: "hsl(var(--chart-1))",
  },
  "rate-secondary": {
    label: "Secondary Rate",
    color: "hsl(var(--chart-2))",
  },
} as const;

// Custom tooltip content component
const CustomTooltipContent = ({
  active,
  payload,
  label,
  formatRate,
  primaryRateLabel,
  secondaryRateLabel,
}: {
  active?: boolean;
  payload?: unknown[];
  label?: string;
  formatRate: (value: number) => string;
  primaryRateLabel: string;
  secondaryRateLabel: string;
}) => {
  if (!active || !payload?.length) return null;

  // Safely extract data from payload
  const firstPayload = payload[0] as { payload?: ChartDataItem } | undefined;
  const data = firstPayload?.payload;
  if (!data) return null;

  const isDualRate = data.isDual;

  return (
    <div className="rounded-lg border bg-background p-3 shadow-md">
      <div className="mb-2">
        <div className="font-medium text-foreground">{label}</div>
        <div className="text-xs text-muted-foreground">
          Range: {formatRate(data.min || 0)} - {formatRate(data.max || 0)}
        </div>
      </div>

      <div className="space-y-2">
        {isDualRate ? (
          <>
            <div className="flex items-center gap-2">
              <div
                className="size-3 rounded-sm"
                style={{ backgroundColor: "hsl(var(--chart-1))" }}
              />
              <span className="text-sm font-medium">{primaryRateLabel}:</span>
              <span className="text-sm">
                {formatRate(data["rate-primary"] || 0)}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div
                className="size-3 rounded-sm"
                style={{ backgroundColor: "hsl(var(--chart-2))" }}
              />
              <span className="text-sm font-medium">{secondaryRateLabel}:</span>
              <span className="text-sm">
                {formatRate(data["rate-secondary"] ?? 0)}
              </span>
            </div>
            <div className="mt-2 border-t pt-2 text-xs text-muted-foreground">
              Difference:{" "}
              {formatRate(
                Math.abs(
                  (data["rate-primary"] ?? 0) - (data["rate-secondary"] ?? 0),
                ),
              )}
            </div>
          </>
        ) : (
          <div className="flex items-center gap-2">
            <div
              className="size-3 rounded-sm"
              style={{ backgroundColor: "hsl(var(--chart-1))" }}
            />
            <span className="text-sm font-medium">Rate:</span>
            <span className="text-sm">
              {formatRate(data["rate-primary"] || 0)}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export function PaymentRangeChart({
  ranges,
  currency = "$",
  unit = "",
  showXAxis = false,
  showYAxis = false,
  loading = false,
  className,
  primaryRateLabel = "Primary",
  secondaryRateLabel = "Secondary",
}: PaymentRangeChartProps) {
  // Memoized rate formatter
  const formatRate = useCallback(
    (value: number): string => {
      if (!isValidNumber(value)) {
        return `${currency}--${unit ? ` ${unit}` : ""}`;
      }
      return `${currency}${value.toLocaleString()}${unit ? ` ${unit}` : ""}`;
    },
    [currency, unit],
  );

  // Memoized valid ranges
  const validRanges = useMemo(() => {
    return ranges.filter((range) => {
      if (!range.id || !range.label) return false;
      if (!isValidNumber(range.min) || !isValidNumber(range.max)) return false;
      if (range.max <= range.min) return false;

      // Validate rates
      if (Array.isArray(range.rates)) {
        return (
          range.rates.length === 2 &&
          isValidNumber(range.rates[0]) &&
          isValidNumber(range.rates[1])
        );
      }
      return isValidNumber(range.rates);
    });
  }, [ranges]);

  // Memoized chart data transformation
  const chartData = useMemo((): ChartDataItem[] => {
    return validRanges.map((range) => {
      const isDualRate = Array.isArray(range.rates);

      if (isDualRate) {
        const rates = range.rates as [number, number];
        return {
          name: range.label,
          "rate-primary": rates[0],
          "rate-secondary": rates[1],
          min: range.min,
          max: range.max,
          isDual: true,
        };
      } else {
        return {
          name: range.label,
          "rate-primary": range.rates as number,
          "rate-secondary": null,
          min: range.min,
          max: range.max,
          isDual: false,
        };
      }
    });
  }, [validRanges]);

  // Memoized dual rates detection
  const hasDualRates = useMemo((): boolean => {
    return validRanges.some((range) => Array.isArray(range.rates));
  }, [validRanges]);

  // Memoized tooltip component with formatRate and labels
  const TooltipComponent = useCallback(
    (props: { active?: boolean; payload?: unknown[]; label?: string }) => (
      <CustomTooltipContent
        {...props}
        formatRate={formatRate}
        primaryRateLabel={primaryRateLabel}
        secondaryRateLabel={secondaryRateLabel}
      />
    ),
    [formatRate, primaryRateLabel, secondaryRateLabel],
  );

  // Early returns for loading and error states
  if (loading) {
    return (
      <div className={cn("w-full min-w-[300px]", className)}>
        <div className="space-y-4">
          {/* Chart header skeleton */}
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>

          {/* Chart skeleton */}
          <div className="space-y-3">
            {/* Y-axis skeleton (if shown) */}
            {showYAxis && (
              <div className="flex items-end gap-2">
                <div className="flex flex-col gap-2 text-right">
                  <Skeleton className="h-3 w-12" />
                  <Skeleton className="h-3 w-10" />
                  <Skeleton className="h-3 w-8" />
                  <Skeleton className="h-3 w-6" />
                </div>
                <div className="flex-1" />
              </div>
            )}

            {/* Chart bars skeleton */}
            <div className="flex h-48 items-end justify-between gap-2 px-4">
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={i}
                  className="flex flex-1 flex-col items-center gap-2"
                >
                  {/* Bar skeleton with random heights */}
                  <div className="flex w-full items-end justify-center gap-1">
                    <Skeleton
                      className="w-8 rounded-t"
                      style={{
                        height: `${Math.floor(Math.random() * 80) + 40}px`,
                      }}
                    />
                    {/* Second bar for dual rates (sometimes) */}
                    {Math.random() > 0.5 && (
                      <Skeleton
                        className="w-8 rounded-t"
                        style={{
                          height: `${Math.floor(Math.random() * 80) + 40}px`,
                        }}
                      />
                    )}
                  </div>

                  {/* X-axis label skeleton (if shown) */}
                  {showXAxis && <Skeleton className="h-3 w-12" />}
                </div>
              ))}
            </div>

            {/* Legend skeleton */}
            <div className="flex items-center justify-center gap-6">
              <div className="flex items-center gap-2">
                <Skeleton className="size-3 rounded-sm" />
                <Skeleton className="h-3 w-16" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="size-3 rounded-sm" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (validRanges.length === 0) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex h-20 items-center justify-center text-sm text-muted-foreground">
          No valid payment ranges provided
        </div>
      </div>
    );
  }

  // Main render block
  return (
    <div className={cn("w-full min-w-[300px]", className)}>
      <ChartContainer config={CHART_CONFIG}>
        <BarChart accessibilityLayer data={chartData}>
          <CartesianGrid
            vertical={false}
            stroke="hsl(var(--muted-foreground))"
            strokeOpacity={0.5}
          />
          <XAxis
            hide={!showXAxis}
            dataKey="name"
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            interval={0}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis
            hide={!showYAxis}
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            tickFormatter={formatRate}
          />
          <ChartTooltip cursor={false} content={TooltipComponent} />

          {/* Always show primary rate bar */}
          <Bar
            dataKey="rate-primary"
            fill="var(--color-rate-primary)"
            radius={4}
          />

          {/* Conditionally show secondary rate bar for dual rates */}
          {hasDualRates && (
            <Bar
              dataKey="rate-secondary"
              fill="var(--color-rate-secondary)"
              radius={4}
            />
          )}
        </BarChart>
      </ChartContainer>
    </div>
  );
}
