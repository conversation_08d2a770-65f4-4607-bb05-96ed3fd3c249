import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { StatusData } from "@/ui/charts/status-progress";

import { StatusProgress } from "@/ui/charts/status-progress";

const meta: Meta<typeof StatusProgress> = {
  title: "Charts/StatusProgress",
  component: StatusProgress,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A reusable radial bar chart component for displaying progress across multiple statuses. Perfect for showing completion rates, work order progress, or any multi-step process status.",
      },
    },
  },
  argTypes: {
    statuses: {
      description:
        "Array of status objects with key, label, current, and total values",
      control: { type: "object" },
    },
    unitLabel: {
      description:
        "Label for the unit being measured (e.g., 'orders', 'tasks', 'items')",
      control: { type: "text" },
    },
    className: {
      description: "Additional CSS classes for styling",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof StatusProgress>;

// Work Order Status Example
const workOrderStatuses: StatusData[] = [
  { key: "reviewed", label: "Reviewed", current: 12, total: 15 },
  { key: "assigned", label: "Assigned", current: 8, total: 12 },
  { key: "scheduled", label: "Scheduled", current: 5, total: 8 },
  { key: "completed", label: "Completed", current: 3, total: 5 },
];

// Project Management Example
const projectStatuses: StatusData[] = [
  { key: "planning", label: "Planning", current: 5, total: 5 },
  { key: "development", label: "Development", current: 8, total: 12 },
  { key: "testing", label: "Testing", current: 3, total: 8 },
  { key: "deployed", label: "Deployed", current: 1, total: 3 },
];

// Medical Appointment Example
const appointmentStatuses: StatusData[] = [
  { key: "booked", label: "Booked", current: 45, total: 50 },
  { key: "confirmed", label: "Confirmed", current: 38, total: 45 },
  { key: "checked_in", label: "Checked In", current: 22, total: 38 },
  { key: "completed", label: "Completed", current: 18, total: 22 },
];

// Two Status Example
const twoStatusExample: StatusData[] = [
  { key: "pending", label: "Pending Review", current: 15, total: 20 },
  { key: "approved", label: "Approved", current: 12, total: 15 },
];

export const WorkOrders: Story = {
  args: {
    statuses: workOrderStatuses,
    unitLabel: "orders",
  },
};

export const ProjectManagement: Story = {
  args: {
    statuses: projectStatuses,
    unitLabel: "tasks",
  },
};

export const MedicalAppointments: Story = {
  args: {
    statuses: appointmentStatuses,
    unitLabel: "appointments",
  },
};

export const TwoStatuses: Story = {
  args: {
    statuses: twoStatusExample,
    unitLabel: "requests",
  },
};

export const EmptyData: Story = {
  args: {
    statuses: [
      { key: "pending", label: "Pending", current: 0, total: 0 },
      { key: "completed", label: "Completed", current: 0, total: 0 },
    ],
    unitLabel: "items",
  },
};

export const HighVolume: Story = {
  args: {
    statuses: [
      { key: "received", label: "Received", current: 1250, total: 1500 },
      { key: "processing", label: "Processing", current: 980, total: 1250 },
      { key: "approved", label: "Approved", current: 750, total: 980 },
      { key: "disbursed", label: "Disbursed", current: 600, total: 750 },
    ],
    unitLabel: "claims",
  },
};

export const FiveStatuses: Story = {
  args: {
    statuses: [
      { key: "submitted", label: "Submitted", current: 25, total: 30 },
      { key: "reviewed", label: "Under Review", current: 20, total: 25 },
      { key: "approved", label: "Approved", current: 15, total: 20 },
      { key: "scheduled", label: "Scheduled", current: 12, total: 15 },
      { key: "completed", label: "Completed", current: 8, total: 12 },
    ],
    unitLabel: "applications",
  },
};

export const CustomStyling: Story = {
  args: {
    statuses: workOrderStatuses,
    unitLabel: "orders",
    className: "w-full max-w-2xl",
  },
};
