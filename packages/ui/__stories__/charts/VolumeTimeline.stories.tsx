import type { <PERSON>a, StoryObj } from "@storybook/react";

import { format, subDays } from "date-fns";

import type { VolumeEntry } from "@/ui/charts/volume-timeline";

import { VolumeTimeline } from "@/ui/charts/volume-timeline";

const meta: Meta<typeof VolumeTimeline> = {
  title: "Charts/VolumeTimeline",
  component: VolumeTimeline,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A reusable line chart component for displaying volume data over time with summary cards showing today's count and period total. Perfect for work orders, sales, appointments, or any time-series volume data.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Array of volume entries with date and volume values",
      control: { type: "object" },
    },
    todayLabel: {
      description: "Label for the today summary card",
      control: { type: "text" },
    },
    periodLabel: {
      description: "Label for the period summary card",
      control: { type: "text" },
    },
    unitLabel: {
      description:
        "Unit label displayed next to the numbers (e.g., 'orders', 'sales', 'calls')",
      control: { type: "text" },
    },
    chartLabel: {
      description: "Label for the chart legend",
      control: { type: "text" },
    },
    timelineLabels: {
      description: "Labels for the timeline at the bottom of the chart",
      control: { type: "object" },
    },
    className: {
      description: "Additional CSS classes for styling",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof VolumeTimeline>;

// Generate sample data for different scenarios
const generateVolumeData = (
  days: number,
  baseVolume: number,
  variance: number,
): VolumeEntry[] => {
  const data: VolumeEntry[] = [];
  for (let i = days; i >= 0; i--) {
    const date = format(subDays(new Date(), i), "yyyy-MM-dd");
    const volume = Math.max(
      0,
      Math.floor(baseVolume + (Math.random() - 0.5) * variance),
    );
    data.push({ date, volume });
  }
  return data;
};

// Work Orders Example
const workOrderData = generateVolumeData(14, 25, 15);

// Sales Volume Example
const salesData = generateVolumeData(30, 150, 80);

// Appointment Scheduling Example
const appointmentData = generateVolumeData(7, 12, 8);

// High Volume Example
const highVolumeData = generateVolumeData(21, 500, 200);

// Low Volume Example
const lowVolumeData = generateVolumeData(10, 3, 2);

export const WorkOrders: Story = {
  args: {
    data: workOrderData,
    todayLabel: "Orders today",
    periodLabel: "Last 2 weeks",
    unitLabel: "orders",
    chartLabel: "Work Orders",
  },
};

export const SalesVolume: Story = {
  args: {
    data: salesData,
    todayLabel: "Sales today",
    periodLabel: "This month",
    unitLabel: "sales",
    chartLabel: "Sales Volume",
    timelineLabels: {
      start: "month start",
      middle: "today",
      end: "month end",
    },
  },
};

export const MedicalAppointments: Story = {
  args: {
    data: appointmentData,
    todayLabel: "Appointments today",
    periodLabel: "This week",
    unitLabel: "appointments",
    chartLabel: "Medical Appointments",
    timelineLabels: {
      start: "week start",
      middle: "today",
      end: "week end",
    },
  },
};

export const HighVolumeData: Story = {
  args: {
    data: highVolumeData,
    todayLabel: "Calls today",
    periodLabel: "Last 3 weeks",
    unitLabel: "calls",
    chartLabel: "Customer Calls",
  },
};

export const LowVolumeData: Story = {
  args: {
    data: lowVolumeData,
    todayLabel: "Issues today",
    periodLabel: "Last 10 days",
    unitLabel: "issues",
    chartLabel: "Support Issues",
  },
};

export const EmptyData: Story = {
  args: {
    data: [],
    todayLabel: "Volume today",
    periodLabel: "This period",
    unitLabel: "items",
    chartLabel: "Volume",
  },
};

export const CustomLabels: Story = {
  args: {
    data: workOrderData,
    todayLabel: "📊 Current Day",
    periodLabel: "🗓️ Full Period",
    unitLabel: "units",
    chartLabel: "Custom Volume",
    timelineLabels: {
      start: "📅 start",
      middle: "📍 now",
      end: "🔮 future",
    },
  },
};

export const WithCustomStyling: Story = {
  args: {
    data: salesData,
    todayLabel: "Daily revenue",
    periodLabel: "Monthly revenue",
    unitLabel: "transactions",
    chartLabel: "Revenue Volume",
    className: "max-w-4xl border-2 border-blue-200",
  },
};
