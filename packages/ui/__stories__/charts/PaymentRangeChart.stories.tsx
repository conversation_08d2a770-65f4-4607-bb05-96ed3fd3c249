import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { PaymentRange } from "@/ui/charts/payment-range";

import { PaymentRangeChart } from "@/ui/charts/payment-range";

const meta: Meta<typeof PaymentRangeChart> = {
  title: "Charts/PaymentRange",
  component: PaymentRangeChart,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A reusable bar chart component for displaying payment ranges with optional dual rate comparison. Perfect for salary ranges, hourly rates, or any comparative payment data.",
      },
    },
  },
  argTypes: {
    ranges: {
      description:
        "Array of payment range objects with rates and min/max values",
      control: { type: "object" },
    },
    currency: {
      description: "Currency symbol to display",
      control: { type: "text" },
    },
    unit: {
      description: "Unit label (e.g., 'per hour', 'per month')",
      control: { type: "text" },
    },
    showXAxis: {
      description: "Whether to show the X-axis with range labels",
      control: { type: "boolean" },
    },
    showYAxis: {
      description: "Whether to show the Y-axis with rate values",
      control: { type: "boolean" },
    },
    loading: {
      description: "Whether to show loading state",
      control: { type: "boolean" },
    },
    className: {
      description: "Additional CSS classes for styling",
      control: { type: "text" },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-full max-w-4xl p-6">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof PaymentRangeChart>;

// Tech Role Annual Salaries (consistent scale)
const techSalaryRanges: PaymentRange[] = [
  {
    id: "intern",
    label: "Intern",
    min: 35000,
    max: 45000,
    rates: [40000, 45000],
  },
  {
    id: "junior",
    label: "Junior Developer",
    min: 45000,
    max: 65000,
    rates: [55000, 60000],
  },
  {
    id: "mid",
    label: "Mid-Level",
    min: 65000,
    max: 85000,
    rates: [72000, 78000], // Frontend vs Backend
  },
  {
    id: "senior",
    label: "Senior Developer",
    min: 85000,
    max: 120000,
    rates: [95000, 110000], // Frontend vs Backend
  },
  {
    id: "lead",
    label: "Tech Lead",
    min: 120000,
    max: 150000,
    rates: [135000, 145000],
  },
  {
    id: "principal",
    label: "Principal Engineer",
    min: 150000,
    max: 200000,
    rates: [165000, 185000], // IC vs Management track
  },
  {
    id: "architect",
    label: "Software Architect",
    min: 180000,
    max: 250000,
    rates: [210000, 230000], // Solutions vs Enterprise
  },
  {
    id: "director",
    label: "Engineering Director",
    min: 200000,
    max: 300000,
    rates: [245000, 255000],
  },
];

// Healthcare Hourly Rates (consistent scale, realistic ranges)
const healthcareHourlyRates: PaymentRange[] = [
  {
    id: "cna",
    label: "CNA",
    min: 15,
    max: 22,
    rates: [18, 20], // Day vs Night shift
  },
  {
    id: "lpn",
    label: "LPN",
    min: 22,
    max: 30,
    rates: [25, 28], // Day vs Night shift
  },
  {
    id: "rn",
    label: "RN",
    min: 30,
    max: 45,
    rates: [35, 42], // Day vs Night shift
  },
  {
    id: "bsn",
    label: "BSN",
    min: 35,
    max: 50,
    rates: [40, 47], // Med-Surg vs ICU
  },
  {
    id: "charge_nurse",
    label: "Charge Nurse",
    min: 45,
    max: 60,
    rates: [50, 55], // Day vs Night
  },
  {
    id: "nurse_practitioner",
    label: "Nurse Practitioner",
    min: 50,
    max: 75,
    rates: [58, 68], // Outpatient vs Hospital
  },
  {
    id: "physician_assistant",
    label: "Physician Assistant",
    min: 55,
    max: 80,
    rates: [65, 75], // Primary vs Specialty
  },
  {
    id: "resident",
    label: "Medical Resident",
    min: 60,
    max: 70,
    rates: [65, 70], // Day vs Night
  },
];

// Freelance/Contract Hourly Rates (consistent scale)
const freelanceHourlyRates: PaymentRange[] = [
  {
    id: "data_entry",
    label: "Data Entry",
    min: 12,
    max: 18,
    rates: [15, 18], // Basic vs Experienced
  },
  {
    id: "virtual_assistant",
    label: "Virtual Assistant",
    min: 15,
    max: 25,
    rates: [18, 22], // Basic vs Specialized
  },
  {
    id: "content_writing",
    label: "Content Writing",
    min: 20,
    max: 40,
    rates: [25, 35], // Blog vs Technical
  },
  {
    id: "graphic_design",
    label: "Graphic Design",
    min: 25,
    max: 50,
    rates: [30, 45], // Template vs Custom
  },
  {
    id: "web_design",
    label: "Web Design",
    min: 35,
    max: 65,
    rates: [45, 55], // Basic vs Premium
  },
  {
    id: "digital_marketing",
    label: "Digital Marketing",
    min: 40,
    max: 70,
    rates: [50, 65], // Campaign vs Strategy
  },
  {
    id: "web_development",
    label: "Web Development",
    min: 45,
    max: 85,
    rates: [55, 75], // Frontend vs Fullstack
  },
  {
    id: "mobile_development",
    label: "Mobile Development",
    min: 55,
    max: 95,
    rates: [65, 85], // Hybrid vs Native
  },
];

// Senior Tech Consulting (higher scale, but consistent)
const seniorTechConsulting: PaymentRange[] = [
  {
    id: "senior_dev",
    label: "Senior Developer",
    min: 75,
    max: 120,
    rates: [85, 105], // Remote vs Onsite
  },
  {
    id: "tech_lead",
    label: "Technical Lead",
    min: 100,
    max: 150,
    rates: [125, 135], // IC vs Management
  },
  {
    id: "solutions_architect",
    label: "Solutions Architect",
    min: 120,
    max: 180,
    rates: [140, 165], // Integration vs Enterprise
  },
  {
    id: "data_architect",
    label: "Data Architect",
    min: 130,
    max: 190,
    rates: [150, 175], // Analytics vs ML
  },
  {
    id: "security_consultant",
    label: "Security Consultant",
    min: 140,
    max: 200,
    rates: [160, 185], // Assessment vs Implementation
  },
  {
    id: "cloud_architect",
    label: "Cloud Architect",
    min: 150,
    max: 210,
    rates: [170, 195], // Migration vs Greenfield
  },
  {
    id: "ai_ml_consultant",
    label: "AI/ML Consultant",
    min: 160,
    max: 220,
    rates: [180, 205], // Research vs Implementation
  },
  {
    id: "enterprise_architect",
    label: "Enterprise Architect",
    min: 170,
    max: 250,
    rates: [210, 220], // Technical vs Business
  },
];

// Service Package Rates (single scale)
const servicePackageRates: PaymentRange[] = [
  {
    id: "basic",
    label: "Basic Service",
    min: 25,
    max: 40,
    rates: [32, 35], // Self-service vs Managed
  },
  {
    id: "standard",
    label: "Standard Package",
    min: 40,
    max: 65,
    rates: [48, 58], // Monthly vs Annual
  },
  {
    id: "premium",
    label: "Premium Service",
    min: 65,
    max: 95,
    rates: [80, 85], // Standard vs Custom
  },
  {
    id: "enterprise",
    label: "Enterprise Solution",
    min: 95,
    max: 150,
    rates: [120, 140], // Standard vs Custom
  },
  {
    id: "white_label",
    label: "White Label",
    min: 140,
    max: 200,
    rates: [160, 185], // Basic vs Full
  },
];

// Single Rate Examples (for comparison)
const singleRateExamples: PaymentRange[] = [
  {
    id: "intern",
    label: "Intern",
    min: 35000,
    max: 45000,
    rates: 40000,
  },
  {
    id: "junior",
    label: "Junior Developer",
    min: 45000,
    max: 65000,
    rates: 55000,
  },
  {
    id: "mid",
    label: "Mid-Level",
    min: 65000,
    max: 85000,
    rates: 75000,
  },
  {
    id: "senior",
    label: "Senior Developer",
    min: 85000,
    max: 120000,
    rates: 100000,
  },
  {
    id: "lead",
    label: "Tech Lead",
    min: 120000,
    max: 150000,
    rates: 135000,
  },
];

export const TechSalaries: Story = {
  args: {
    ranges: techSalaryRanges,
    currency: "$",
    unit: "annually",
  },
};

export const HealthcareHourly: Story = {
  args: {
    ranges: healthcareHourlyRates,
    currency: "$",
    unit: "per hour",
  },
};

export const FreelanceRates: Story = {
  args: {
    ranges: freelanceHourlyRates,
    currency: "$",
    unit: "per hour",
  },
};

export const SeniorConsulting: Story = {
  args: {
    ranges: seniorTechConsulting,
    currency: "$",
    unit: "per hour",
  },
};

export const ServicePackages: Story = {
  args: {
    ranges: servicePackageRates,
    currency: "$",
    unit: "per hour",
  },
};

export const SingleRatesOnly: Story = {
  args: {
    ranges: singleRateExamples,
    currency: "$",
    unit: "annually",
  },
};

export const DualRatesOnly: Story = {
  args: {
    ranges: healthcareHourlyRates.filter((range) => Array.isArray(range.rates)),
    currency: "$",
    unit: "per hour",
  },
};

export const EuropeanSalaries: Story = {
  args: {
    ranges: [
      {
        id: "trainee",
        label: "Trainee",
        min: 28000,
        max: 35000,
        rates: [32000, 35000],
      },
      {
        id: "junior",
        label: "Junior Developer",
        min: 35000,
        max: 50000,
        rates: [38000, 45000], // Frontend vs Backend
      },
      {
        id: "mid",
        label: "Mid-Level",
        min: 50000,
        max: 70000,
        rates: [55000, 65000], // Frontend vs Backend
      },
      {
        id: "senior",
        label: "Senior Developer",
        min: 70000,
        max: 95000,
        rates: [78000, 88000], // Frontend vs Backend
      },
      {
        id: "lead",
        label: "Team Lead",
        min: 85000,
        max: 110000,
        rates: [92000, 105000], // IC vs Management
      },
      {
        id: "principal",
        label: "Principal Engineer",
        min: 100000,
        max: 140000,
        rates: [115000, 130000], // IC vs Management
      },
      {
        id: "architect",
        label: "Solutions Architect",
        min: 120000,
        max: 160000,
        rates: [135000, 150000], // Technical vs Business
      },
      {
        id: "director",
        label: "Engineering Director",
        min: 140000,
        max: 200000,
        rates: [170000, 180000],
      },
    ],
    currency: "€",
    unit: "annually",
  },
};

export const LoadingState: Story = {
  args: {
    ranges: techSalaryRanges,
    currency: "$",
    unit: "annually",
    loading: true,
  },
};

export const NoXAxis: Story = {
  args: {
    ranges: servicePackageRates,
    currency: "$",
    unit: "per hour",
    showXAxis: false,
  },
};

export const CompactView: Story = {
  args: {
    ranges: [
      {
        id: "basic",
        label: "Basic",
        min: 25,
        max: 40,
        rates: [32, 38], // Self-service vs Managed
      },
      {
        id: "pro",
        label: "Professional",
        min: 45,
        max: 75,
        rates: [55, 68], // Standard vs Premium
      },
      {
        id: "expert",
        label: "Expert",
        min: 80,
        max: 120,
        rates: [95, 110], // Consultation vs Implementation
      },
    ],
    currency: "$",
    unit: "/hr",
    className: "max-w-md",
  },
};
