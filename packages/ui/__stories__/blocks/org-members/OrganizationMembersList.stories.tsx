import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import {
  Mail,
  MoreHorizontal,
  Settings,
  Trash2,
  UserPlus2,
} from "lucide-react";

import type {
  GenericMember,
  GenericMemberWithOriginal,
} from "@/ui/blocks/org-members";

import {
  createMemberAdapter,
  OrganizationMembersList,
} from "@/ui/blocks/org-members";
import { Button } from "@/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/primitives/dropdown-menu";

const meta: Meta<typeof OrganizationMembersList> = {
  title: "Blocks/Organization Members List",
  component: OrganizationMembersList,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A generic, reusable component for displaying lists of organization members with customizable rendering and behavior.",
      },
    },
  },
  argTypes: {
    gridCols: {
      control: "select",
      options: [1, 2, 3, 4],
      description: "Number of grid columns for member cards",
    },
    title: {
      control: "text",
      description: "Header title for the members list",
    },
    description: {
      control: "text",
      description: "Description text shown below the title",
    },
    emptyMessage: {
      control: "text",
      description: "Message shown when no members are present",
    },
    loading: {
      control: "boolean",
      description: "Whether the component is in a loading state",
    },
  },
};

export default meta;
type Story = StoryObj<typeof OrganizationMembersList>;

// Mock data generators
function createMockMember(overrides?: Partial<GenericMember>): GenericMember {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const roles = ["Admin", "Member", "Manager", "Viewer", "Editor"];
  const statuses = ["Active", "Pending", "Invited"];

  return {
    id: faker.string.uuid(),
    displayName: `${firstName} ${lastName}`,
    avatar: faker.helpers.maybe(() => faker.image.avatar(), {
      probability: 0.7,
    }),
    initials: `${firstName[0]}${lastName[0]}`,
    role: faker.helpers.arrayElement(roles),
    status: faker.helpers.arrayElement(statuses),
    metadata: {
      email: faker.internet.email({ firstName, lastName }),
      joinedDate: faker.date.recent().toISOString(),
      department: faker.commerce.department(),
    },
    ...overrides,
  };
}

function createMockMembers(count: number): GenericMember[] {
  return Array.from({ length: count }, () => createMockMember());
}

// Example domain-specific type for stories
interface ExampleOrgMember {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
  role: "org:admin" | "org:member";
  status: "ACTIVE" | "PENDING" | "INVITED";
  joinedAt: string;
}

function createMockOrgMember(): ExampleOrgMember {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();

  return {
    id: faker.string.uuid(),
    user: {
      firstName,
      lastName,
      email: faker.internet.email({ firstName, lastName }),
      avatar: faker.helpers.maybe(() => faker.image.avatar(), {
        probability: 0.6,
      }),
    },
    role: faker.helpers.arrayElement(["org:admin", "org:member"]),
    status: faker.helpers.arrayElement(["ACTIVE", "PENDING", "INVITED"]),
    joinedAt: faker.date.recent().toISOString(),
  };
}

// Custom menu component for stories
function ExampleMemberMenu({ member }: { member: GenericMember }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="size-8 p-0">
          <MoreHorizontal className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem>
          <Mail className="mr-2 size-4" />
          Send Message
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 size-4" />
          Edit Role
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-destructive">
          <Trash2 className="mr-2 size-4" />
          Remove Member
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Default story
export const Default: Story = {
  args: {
    members: createMockMembers(6),
    total: 6,
    title: "Team Members",
    description: "Manage organization members and their roles",
    gridCols: 2,
  },
};

// Loading state
export const Loading: Story = {
  args: {
    members: [],
    total: 0,
    loading: true,
    title: "Team Members",
    description: "Manage organization members and their roles",
  },
};

// Empty state
export const Empty: Story = {
  args: {
    members: [],
    total: 0,
    title: "Team Members",
    description: "Manage organization members and their roles",
    emptyMessage:
      "No team members have been added yet. Invite your first member to get started!",
  },
};

// Error state
export const ErrorState: Story = {
  args: {
    members: [],
    total: 0,
    error: new Error("Failed to load team members. Please try again."),
    title: "Team Members",
    description: "Manage organization members and their roles",
  },
};

// With custom actions
export const WithActions: Story = {
  args: {
    members: createMockMembers(4),
    total: 4,
    title: "Team Members",
    description: "Manage organization members and their roles",
    renderActions: () => (
      <div className="flex gap-2">
        <Button variant="outline" size="sm">
          <UserPlus2 className="mr-2 size-4" />
          Invite Member
        </Button>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 size-4" />
          Settings
        </Button>
      </div>
    ),
  },
};

// With member menu
export const WithMemberMenu: Story = {
  args: {
    members: createMockMembers(4),
    total: 4,
    title: "Team Members",
    description: "Manage organization members and their roles",
    renderMemberMenu: (member) => <ExampleMemberMenu member={member} />,
  },
};

// Different grid layouts
export const SingleColumn: Story = {
  args: {
    members: createMockMembers(3),
    total: 3,
    title: "Team Members",
    description: "Single column layout",
    gridCols: 1,
  },
};

export const ThreeColumns: Story = {
  args: {
    members: createMockMembers(9),
    total: 9,
    title: "Team Members",
    description: "Three column layout",
    gridCols: 3,
  },
};

export const FourColumns: Story = {
  args: {
    members: createMockMembers(12),
    total: 12,
    title: "Team Members",
    description: "Four column layout",
    gridCols: 4,
  },
};

// With pagination
export const WithPagination: Story = {
  args: {
    members: createMockMembers(4),
    total: 47,
    title: "Team Members",
    description: "Showing paginated results",
    pagination: {
      pageIndex: 0,
      pageSize: 4,
    },
    onPaginationChange: (pagination) => {
      console.log("Pagination changed:", pagination);
    },
    itemNoun: { singular: "member", plural: "members" },
  },
};

// Using the adapter pattern with domain-specific data
export const WithAdapter: Story = {
  render: () => {
    // Create mock domain-specific data
    const orgMembers = Array.from({ length: 5 }, () => createMockOrgMember());

    // Adapt to generic format
    const adaptedMembers = orgMembers.map((member) =>
      createMemberAdapter(member, {
        getId: (m) => m.id,
        getDisplayName: (m) => `${m.user.firstName} ${m.user.lastName}`,
        getAvatar: (m) => m.user.avatar,
        getInitials: (m) => `${m.user.firstName[0]}${m.user.lastName[0]}`,
        getRole: (m) => (m.role === "org:admin" ? "Admin" : "Member"),
        getStatus: (m) => m.status,
        getMetadata: (m) => ({
          email: m.user.email,
          joinedAt: m.joinedAt,
        }),
      }),
    );

    return (
      <OrganizationMembersList
        members={adaptedMembers}
        total={adaptedMembers.length}
        title="Organization Members"
        description="Using adapter pattern with domain-specific data"
        renderMemberMenu={(member) => <ExampleMemberMenu member={member} />}
        renderActions={() => (
          <Button variant="outline">
            <UserPlus2 className="mr-2 size-4" />
            Add Member
          </Button>
        )}
        onMemberClick={(member) => {
          console.log("Clicked member:", member);
          // Access original data: member.originalData
        }}
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing how to use the adapter pattern to transform domain-specific data into the generic format.",
      },
    },
  },
};

// Fully customized rendering
export const CustomRendering: Story = {
  args: {
    members: createMockMembers(4),
    total: 4,
    title: "Custom Team Display",
    description: "Example with fully custom member card rendering",
    renderMember: (member) => (
      <div
        key={member.id}
        className="group relative rounded-lg border-2 border-dashed border-muted-foreground/20 p-6 text-center transition-all hover:border-primary hover:bg-accent"
      >
        <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-primary/10 text-2xl font-bold text-primary">
          {member.initials}
        </div>
        <h3 className="font-semibold">{member.displayName}</h3>
        <p className="text-sm text-muted-foreground">{member.role}</p>
        {member.status && (
          <span className="mt-2 inline-block rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
            {member.status}
          </span>
        )}
      </div>
    ),
    renderActions: () => (
      <Button>
        <UserPlus2 className="mr-2 size-4" />
        Custom Add Button
      </Button>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing completely custom rendering of member cards while maintaining the same data structure.",
      },
    },
  },
};

// Large dataset
export const LargeDataset: Story = {
  args: {
    members: createMockMembers(20),
    total: 156,
    title: "Large Team",
    description: "Example with many members",
    gridCols: 3,
    pagination: {
      pageIndex: 0,
      pageSize: 20,
    },
    onPaginationChange: (pagination) => {
      console.log("Pagination changed:", pagination);
    },
  },
};

// Different member types
export const MixedMemberTypes: Story = {
  args: {
    members: [
      createMockMember({
        role: "Super Admin",
        status: "Active",
        displayName: "John Smith",
      }),
      createMockMember({
        role: "Admin",
        status: "Active",
        displayName: "Sarah Johnson",
        avatar: null,
      }),
      createMockMember({
        role: "Manager",
        status: "Pending",
        displayName: "Mike Chen",
      }),
      createMockMember({
        role: "Member",
        status: "Invited",
        displayName: "Emily Davis",
        avatar: null,
      }),
      createMockMember({
        role: "Viewer",
        status: "Active",
        displayName: "Alex Wilson",
      }),
    ],
    total: 5,
    title: "Mixed Role Types",
    description: "Example showing different roles and statuses",
  },
};
